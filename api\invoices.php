<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../includes/middleware.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً']);
    exit();
}

class InvoiceManager {
    private $conn;
    private $table_name = "invoices";
    private $items_table = "invoice_items";

    public function __construct($db) {
        $this->conn = $db;
    }

    // Generate next invoice number for user
    public function generateInvoiceNumber($userId) {
        $query = "SELECT MAX(CAST(SUBSTRING(invoice_number, 1) AS UNSIGNED)) as max_num
                  FROM " . $this->table_name . " WHERE user_id = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$userId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        $nextNumber = ($result['max_num'] ?? 0) + 1;
        return str_pad($nextNumber, 6, '0', STR_PAD_LEFT);
    }

    // Get all invoices for user
    public function getAll($userId) {
        $query = "SELECT i.*, c.name as client_name, c.email as client_email
                  FROM " . $this->table_name . " i
                  LEFT JOIN clients c ON i.client_id = c.id
                  WHERE i.user_id = ?
                  ORDER BY i.created_at DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute([$userId]);

        $invoices = array();
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $invoices[] = $row;
        }

        return $invoices;
    }

    // Get invoice by ID with items (with user ownership check)
    public function getById($id, $userId) {
        // Get invoice details
        $query = "SELECT i.*, c.name as client_name, c.email as client_email,
                         c.phone as client_phone, c.address as client_address
                  FROM " . $this->table_name . " i
                  LEFT JOIN clients c ON i.client_id = c.id
                  WHERE i.id = ? AND i.user_id = ? LIMIT 0,1";

        $stmt = $this->conn->prepare($query);
        $stmt->execute([$id, $userId]);

        $invoice = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($invoice) {
            // Get invoice items
            $items_query = "SELECT * FROM " . $this->items_table . " WHERE invoice_id = ?";
            $items_stmt = $this->conn->prepare($items_query);
            $items_stmt->execute([$id]);

            $items = array();
            while ($item = $items_stmt->fetch(PDO::FETCH_ASSOC)) {
                $items[] = $item;
            }

            $invoice['items'] = $items;
        }

        return $invoice;
    }

    // Create new invoice
    public function create($data, $userId) {
        try {
            $this->conn->beginTransaction();

            // Generate invoice number if not provided
            if (empty($data['invoice_number'])) {
                $data['invoice_number'] = $this->generateInvoiceNumber($userId);
            }

            // Insert invoice
            $query = "INSERT INTO " . $this->table_name . "
                      SET user_id=:user_id, invoice_number=:invoice_number, client_id=:client_id,
                          issue_date=:issue_date, due_date=:due_date,
                          reference_number=:reference_number, subtotal=:subtotal,
                          discount_percentage=:discount_percentage, discount_amount=:discount_amount,
                          tax_rate=:tax_rate, tax_amount=:tax_amount,
                          total_amount=:total_amount, amount_due=:amount_due,
                          notes=:notes, terms=:terms, status=:status";

            $stmt = $this->conn->prepare($query);

            // Bind invoice data
            $stmt->bindParam(":user_id", $userId);
            $stmt->bindParam(":invoice_number", $data['invoice_number']);
            $stmt->bindParam(":client_id", $data['client_id']);
            $stmt->bindParam(":issue_date", $data['issue_date']);
            $stmt->bindParam(":due_date", $data['due_date']);
            $stmt->bindParam(":reference_number", $data['reference_number']);
            $stmt->bindParam(":subtotal", $data['subtotal']);
            $stmt->bindParam(":discount_percentage", $data['discount_percentage']);
            $stmt->bindParam(":discount_amount", $data['discount_amount']);
            $stmt->bindParam(":tax_rate", $data['tax_rate']);
            $stmt->bindParam(":tax_amount", $data['tax_amount']);
            $stmt->bindParam(":total_amount", $data['total_amount']);
            $stmt->bindParam(":amount_due", $data['amount_due']);
            $stmt->bindParam(":notes", $data['notes']);
            $stmt->bindParam(":terms", $data['terms']);
            $stmt->bindParam(":status", $data['status']);

            $stmt->execute();
            $invoiceId = $this->conn->lastInsertId();
            
            // Insert invoice items
            if (!empty($data['items'])) {
                $items_query = "INSERT INTO " . $this->items_table . " 
                               SET invoice_id=:invoice_id, item_name=:item_name, 
                                   item_description=:item_description, quantity=:quantity, 
                                   rate=:rate, amount=:amount";
                
                $items_stmt = $this->conn->prepare($items_query);
                
                foreach ($data['items'] as $item) {
                    $items_stmt->bindParam(":invoice_id", $invoiceId);
                    $items_stmt->bindParam(":item_name", $item['name']);
                    $items_stmt->bindParam(":item_description", $item['description']);
                    $items_stmt->bindParam(":quantity", $item['quantity']);
                    $items_stmt->bindParam(":rate", $item['rate']);
                    $items_stmt->bindParam(":amount", $item['amount']);
                    $items_stmt->execute();
                }
            }
            
            $this->conn->commit();
            return $invoiceId;
            
        } catch (Exception $e) {
            $this->conn->rollback();
            return false;
        }
    }

    // Update invoice
    public function update($id, $data) {
        try {
            $this->conn->beginTransaction();
            
            // Update invoice
            $query = "UPDATE " . $this->table_name . " 
                      SET client_id=:client_id, issue_date=:issue_date, due_date=:due_date,
                          reference_number=:reference_number, subtotal=:subtotal,
                          discount_percentage=:discount_percentage, discount_amount=:discount_amount,
                          tax_rate=:tax_rate, tax_amount=:tax_amount,
                          total_amount=:total_amount, amount_due=:amount_due,
                          notes=:notes, terms=:terms, status=:status
                      WHERE id = :id";
            
            $stmt = $this->conn->prepare($query);
            
            // Bind data
            $stmt->bindParam(":id", $id);
            $stmt->bindParam(":client_id", $data['client_id']);
            $stmt->bindParam(":issue_date", $data['issue_date']);
            $stmt->bindParam(":due_date", $data['due_date']);
            $stmt->bindParam(":reference_number", $data['reference_number']);
            $stmt->bindParam(":subtotal", $data['subtotal']);
            $stmt->bindParam(":discount_percentage", $data['discount_percentage']);
            $stmt->bindParam(":discount_amount", $data['discount_amount']);
            $stmt->bindParam(":tax_rate", $data['tax_rate']);
            $stmt->bindParam(":tax_amount", $data['tax_amount']);
            $stmt->bindParam(":total_amount", $data['total_amount']);
            $stmt->bindParam(":amount_due", $data['amount_due']);
            $stmt->bindParam(":notes", $data['notes']);
            $stmt->bindParam(":terms", $data['terms']);
            $stmt->bindParam(":status", $data['status']);
            
            $stmt->execute();
            
            // Delete existing items
            $delete_items = "DELETE FROM " . $this->items_table . " WHERE invoice_id = ?";
            $delete_stmt = $this->conn->prepare($delete_items);
            $delete_stmt->bindParam(1, $id);
            $delete_stmt->execute();
            
            // Insert updated items
            if (!empty($data['items'])) {
                $items_query = "INSERT INTO " . $this->items_table . " 
                               SET invoice_id=:invoice_id, item_name=:item_name, 
                                   item_description=:item_description, quantity=:quantity, 
                                   rate=:rate, amount=:amount";
                
                $items_stmt = $this->conn->prepare($items_query);
                
                foreach ($data['items'] as $item) {
                    $items_stmt->bindParam(":invoice_id", $id);
                    $items_stmt->bindParam(":item_name", $item['name']);
                    $items_stmt->bindParam(":item_description", $item['description']);
                    $items_stmt->bindParam(":quantity", $item['quantity']);
                    $items_stmt->bindParam(":rate", $item['rate']);
                    $items_stmt->bindParam(":amount", $item['amount']);
                    $items_stmt->execute();
                }
            }
            
            $this->conn->commit();
            return true;
            
        } catch (Exception $e) {
            $this->conn->rollback();
            return false;
        }
    }

    // Delete invoice
    public function delete($id) {
        try {
            $this->conn->beginTransaction();
            
            // Delete items first (due to foreign key constraint)
            $delete_items = "DELETE FROM " . $this->items_table . " WHERE invoice_id = ?";
            $items_stmt = $this->conn->prepare($delete_items);
            $items_stmt->bindParam(1, $id);
            $items_stmt->execute();
            
            // Delete invoice
            $delete_invoice = "DELETE FROM " . $this->table_name . " WHERE id = ?";
            $invoice_stmt = $this->conn->prepare($delete_invoice);
            $invoice_stmt->bindParam(1, $id);
            $invoice_stmt->execute();
            
            $this->conn->commit();
            return true;
            
        } catch (Exception $e) {
            $this->conn->rollback();
            return false;
        }
    }

    // Update invoice status
    public function updateStatus($id, $status) {
        $query = "UPDATE " . $this->table_name . " SET status = ? WHERE id = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $status);
        $stmt->bindParam(2, $id);
        
        return $stmt->execute();
    }
}

// Handle API requests
$method = $_SERVER['REQUEST_METHOD'];
$invoiceManager = new InvoiceManager($db);
$currentUser = getCurrentUser();

switch($method) {
    case 'GET':
        if (isset($_GET['id'])) {
            // Get specific invoice
            $invoice = $invoiceManager->getById($_GET['id'], $currentUser['id']);
            if ($invoice) {
                echo json_encode(['success' => true, 'data' => $invoice]);
            } else {
                echo json_encode(['success' => false, 'message' => 'الفاتورة غير موجودة']);
            }
        } elseif (isset($_GET['generate_number'])) {
            // Generate next invoice number
            $number = $invoiceManager->generateInvoiceNumber($currentUser['id']);
            echo json_encode(['success' => true, 'data' => ['invoice_number' => $number]]);
        } else {
            // Get all invoices
            $invoices = $invoiceManager->getAll($currentUser['id']);
            echo json_encode(['success' => true, 'data' => $invoices]);
        }
        break;
        
    case 'POST':
        // Create new invoice
        $data = json_decode(file_get_contents("php://input"), true);

        if (empty($data['issue_date'])) {
            echo json_encode(['success' => false, 'message' => 'تاريخ الإصدار مطلوب']);
            break;
        }

        $invoiceId = $invoiceManager->create($data, $currentUser['id']);
        if ($invoiceId) {
            $invoice = $invoiceManager->getById($invoiceId, $currentUser['id']);
            echo json_encode(['success' => true, 'message' => 'تم إنشاء الفاتورة بنجاح', 'data' => $invoice]);
        } else {
            echo json_encode(['success' => false, 'message' => 'فشل في إنشاء الفاتورة']);
        }
        break;
        
    case 'PUT':
        // Update invoice
        $data = json_decode(file_get_contents("php://input"), true);
        
        if (empty($data['id'])) {
            echo json_encode(['success' => false, 'message' => 'معرف الفاتورة مطلوب']);
            break;
        }
        
        if ($invoiceManager->update($data['id'], $data)) {
            $invoice = $invoiceManager->getById($data['id']);
            echo json_encode(['success' => true, 'message' => 'تم تحديث الفاتورة بنجاح', 'data' => $invoice]);
        } else {
            echo json_encode(['success' => false, 'message' => 'فشل في تحديث الفاتورة']);
        }
        break;
        
    case 'DELETE':
        // Delete invoice
        $data = json_decode(file_get_contents("php://input"), true);
        
        if (empty($data['id'])) {
            echo json_encode(['success' => false, 'message' => 'معرف الفاتورة مطلوب']);
            break;
        }
        
        if ($invoiceManager->delete($data['id'])) {
            echo json_encode(['success' => true, 'message' => 'تم حذف الفاتورة بنجاح']);
        } else {
            echo json_encode(['success' => false, 'message' => 'فشل في حذف الفاتورة']);
        }
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'طريقة غير مدعومة']);
        break;
}
?>
