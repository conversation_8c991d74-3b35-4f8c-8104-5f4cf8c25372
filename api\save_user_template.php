<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../includes/middleware.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً']);
    exit();
}

$currentUser = getCurrentUser();

// قراءة البيانات المرسلة
$input = json_decode(file_get_contents('php://input'), true);
$templateId = $input['template_id'] ?? null;

if (!$templateId) {
    echo json_encode(['success' => false, 'message' => 'معرف القالب مطلوب']);
    exit();
}

try {
    // التحقق من وجود القالب
    $query = "SELECT id FROM invoice_templates WHERE id = ? AND is_active = 1";
    $stmt = $db->prepare($query);
    $stmt->execute([$templateId]);
    
    if (!$stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'القالب غير موجود']);
        exit();
    }
    
    $db->beginTransaction();
    
    // إزالة القالب الافتراضي الحالي
    $query = "DELETE FROM user_template_preferences WHERE user_id = ? AND is_default = 1";
    $stmt = $db->prepare($query);
    $stmt->execute([$currentUser['id']]);
    
    // تعيين القالب الجديد كافتراضي
    $query = "INSERT INTO user_template_preferences (user_id, template_id, is_default) 
              VALUES (?, ?, 1) 
              ON DUPLICATE KEY UPDATE is_default = 1, updated_at = CURRENT_TIMESTAMP";
    $stmt = $db->prepare($query);
    $stmt->execute([$currentUser['id'], $templateId]);
    
    $db->commit();
    
    echo json_encode([
        'success' => true,
        'message' => 'تم حفظ القالب بنجاح'
    ]);
    
} catch (Exception $e) {
    $db->rollback();
    Config::logError("Error saving user template: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ أثناء حفظ القالب'
    ]);
}
?>
