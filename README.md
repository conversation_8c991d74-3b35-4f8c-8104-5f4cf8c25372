# نظام إنشاء الفواتير

نظام شامل لإنشاء وإدارة الفواتير باللغة العربية، مطور باستخدام HTML, CSS, JavaScript, و PHP مع قاعدة بيانات MySQL.

## الميزات

### ✨ الميزات الأساسية
- إنشاء فواتير جديدة بتصميم احترافي
- إدارة العملاء (إضافة، تعديل، حذف)
- حفظ الفواتير في قاعدة البيانات
- عرض قائمة الفواتير مع إمكانية البحث
- طباعة الفواتير وتصديرها
- واجهة مستخدم باللغة العربية مع دعم RTL

### 📋 تفاصيل الفاتورة
- رفع شعار الشركة
- معلومات الشركة والعميل
- جدول العناصر مع الأسعار والكميات
- حساب الضرائب والخصومات تلقائياً
- إضافة ملاحظات وشروط
- حالات مختلفة للفاتورة (مسودة، مرسلة، مدفوعة، متأخرة)

### 🎨 التصميم
- تصميم متجاوب يعمل على جميع الأجهزة
- واجهة مستخدم حديثة وسهلة الاستخدام
- ألوان وخطوط متناسقة
- أيقونات Font Awesome
- خط Cairo للنصوص العربية

## متطلبات النظام

- **خادم ويب**: Apache أو Nginx
- **PHP**: الإصدار 7.4 أو أحدث
- **قاعدة البيانات**: MySQL 5.7 أو أحدث / MariaDB 10.2 أو أحدث
- **المتصفح**: Chrome, Firefox, Safari, Edge (الإصدارات الحديثة)

## التثبيت

### 1. تحميل الملفات
```bash
# نسخ الملفات إلى مجلد الخادم
cp -r * /path/to/your/webserver/
```

### 2. إعداد قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE invoice_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استيراد الجداول
mysql -u username -p invoice_system < database.sql
```

### 3. تكوين الاتصال بقاعدة البيانات
قم بتعديل ملف `config/database.php`:
```php
define('DB_HOST', 'localhost');     // عنوان الخادم
define('DB_NAME', 'invoice_system'); // اسم قاعدة البيانات
define('DB_USER', 'your_username');  // اسم المستخدم
define('DB_PASS', 'your_password');  // كلمة المرور
```

### 4. تعيين الصلاحيات
```bash
# إعطاء صلاحيات الكتابة لمجلد الرفع (إذا كان موجوداً)
chmod 755 uploads/
chown www-data:www-data uploads/
```

## بنية المشروع

```
invoice-system/
├── index.php              # الصفحة الرئيسية - إنشاء فاتورة جديدة
├── invoices.php           # قائمة الفواتير
├── view_invoice.php       # عرض الفاتورة
├── database.sql           # ملف قاعدة البيانات
├── README.md              # ملف التوثيق
├── config/
│   └── database.php       # إعدادات قاعدة البيانات
├── api/
│   ├── clients.php        # API إدارة العملاء
│   └── invoices.php       # API إدارة الفواتير
├── css/
│   └── style.css          # ملفات التصميم
├── js/
│   └── script.js          # ملفات JavaScript
└── uploads/               # مجلد رفع الملفات (اختياري)
```

## الاستخدام

### إنشاء فاتورة جديدة
1. افتح `index.php` في المتصفح
2. قم برفع شعار الشركة (اختياري)
3. أضف معلومات العميل أو اختر عميل موجود
4. أدخل تفاصيل الفاتورة (التاريخ، رقم المرجع، إلخ)
5. أضف عناصر الفاتورة مع الأسعار والكميات
6. أضف الملاحظات والشروط (اختياري)
7. احفظ الفاتورة أو أرسلها

### إدارة العملاء
- انقر على "إنشاء عميل" لإضافة عميل جديد
- املأ معلومات العميل (الاسم، البريد، الهاتف، العنوان)
- احفظ العميل لاستخدامه في الفواتير

### عرض الفواتير
1. افتح `invoices.php` لعرض قائمة الفواتير
2. استخدم البحث للعثور على فاتورة معينة
3. انقر على "عرض" لمشاهدة تفاصيل الفاتورة
4. انقر على "تحرير" لتعديل الفاتورة
5. انقر على "حذف" لحذف الفاتورة

## API المتاح

### العملاء (clients.php)
- `GET /api/clients.php` - جلب جميع العملاء
- `GET /api/clients.php?id=1` - جلب عميل محدد
- `POST /api/clients.php` - إنشاء عميل جديد
- `PUT /api/clients.php` - تحديث عميل
- `DELETE /api/clients.php` - حذف عميل

### الفواتير (invoices.php)
- `GET /api/invoices.php` - جلب جميع الفواتير
- `GET /api/invoices.php?id=1` - جلب فاتورة محددة
- `GET /api/invoices.php?generate_number=1` - توليد رقم فاتورة جديد
- `POST /api/invoices.php` - إنشاء فاتورة جديدة
- `PUT /api/invoices.php` - تحديث فاتورة
- `DELETE /api/invoices.php` - حذف فاتورة

## التخصيص

### تغيير معلومات الشركة
قم بتعديل جدول `company_settings` في قاعدة البيانات:
```sql
UPDATE company_settings SET 
    company_name = 'اسم شركتك',
    company_id = 'رقم التسجيل',
    country = 'البلد',
    address = 'العنوان',
    phone = 'رقم الهاتف',
    email = 'البريد الإلكتروني'
WHERE id = 1;
```

### تخصيص التصميم
- عدّل ملف `css/style.css` لتغيير الألوان والخطوط
- استخدم متغيرات CSS لسهولة التخصيص
- جميع النصوص قابلة للتعديل في ملفات HTML

### إضافة ميزات جديدة
- استخدم API الموجود لبناء ميزات إضافية
- أضف جداول جديدة في قاعدة البيانات حسب الحاجة
- اتبع نفس نمط الكود الموجود

## الأمان

### إجراءات الأمان المطبقة
- تنظيف البيانات المدخلة (Input Sanitization)
- استخدام Prepared Statements لمنع SQL Injection
- التحقق من صحة البيانات قبل الحفظ
- ترميز الإخراج لمنع XSS

### توصيات إضافية
- استخدم HTTPS في الإنتاج
- قم بعمل نسخ احتياطية دورية من قاعدة البيانات
- حدّث PHP وMySQL بانتظام
- استخدم كلمات مرور قوية لقاعدة البيانات

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

**خطأ في الاتصال بقاعدة البيانات:**
- تأكد من صحة بيانات الاتصال في `config/database.php`
- تأكد من تشغيل خدمة MySQL
- تأكد من وجود قاعدة البيانات

**الصفحة لا تظهر بشكل صحيح:**
- تأكد من رفع جميع الملفات
- تحقق من مسارات ملفات CSS و JavaScript
- تأكد من دعم المتصفح للميزات المستخدمة

**لا تظهر البيانات:**
- تحقق من وجود البيانات في قاعدة البيانات
- تأكد من صحة استعلامات SQL
- تحقق من console المتصفح للأخطاء

## الدعم والمساهمة

### الحصول على المساعدة
- راجع هذا الملف للحلول الشائعة
- تحقق من ملفات السجل (logs) للأخطاء
- تأكد من متطلبات النظام

### المساهمة في التطوير
- اتبع معايير الكود الموجودة
- اختبر التغييرات قبل الإرسال
- أضف التوثيق للميزات الجديدة

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتعديل.

---

**ملاحظة:** هذا النظام مصمم للاستخدام التجاري والشخصي. يُنصح بإجراء اختبارات شاملة قبل الاستخدام في بيئة الإنتاج.
