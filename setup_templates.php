<?php
// ملف إعداد نظام القوالب
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>إعداد نظام القوالب</h1>";

try {
    // الاتصال بقاعدة البيانات
    require_once 'config/database.php';
    echo "<p>✅ تم الاتصال بقاعدة البيانات</p>";
    
    // قراءة ملف SQL
    $sqlFile = 'templates_setup.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("ملف SQL غير موجود: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    echo "<p>✅ تم قراءة ملف SQL</p>";
    
    // تقسيم الاستعلامات
    $queries = explode(';', $sql);
    $successCount = 0;
    $errorCount = 0;
    
    echo "<h2>تنفيذ الاستعلامات:</h2>";
    echo "<div style='background: #f5f5f5; padding: 10px; border-radius: 5px; font-family: monospace;'>";
    
    foreach ($queries as $query) {
        $query = trim($query);
        if (empty($query) || strpos($query, '--') === 0) {
            continue; // تجاهل التعليقات والأسطر الفارغة
        }
        
        try {
            $stmt = $db->prepare($query);
            $stmt->execute();
            
            // إذا كان الاستعلام SELECT، عرض النتائج
            if (stripos($query, 'SELECT') === 0) {
                $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
                if (!empty($results)) {
                    echo "<strong>نتائج الاستعلام:</strong><br>";
                    foreach ($results as $row) {
                        echo "- " . implode(' | ', $row) . "<br>";
                    }
                }
            }
            
            $successCount++;
            echo "✅ نجح: " . substr($query, 0, 50) . "...<br>";
            
        } catch (Exception $e) {
            $errorCount++;
            echo "❌ فشل: " . substr($query, 0, 50) . "... - " . $e->getMessage() . "<br>";
        }
    }
    
    echo "</div>";
    echo "<h2>ملخص النتائج:</h2>";
    echo "<p>✅ استعلامات ناجحة: $successCount</p>";
    echo "<p>❌ استعلامات فاشلة: $errorCount</p>";
    
    if ($errorCount === 0) {
        echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>🎉 تم إعداد نظام القوالب بنجاح!</h3>";
        echo "<p>يمكنك الآن الوصول إلى صفحة القوالب.</p>";
        echo "</div>";
    }
    
    // اختبار النظام
    echo "<h2>اختبار النظام:</h2>";
    
    // عدد القوالب
    $query = "SELECT COUNT(*) as count FROM invoice_templates";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $count = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p>📊 عدد القوالب المتاحة: " . $count['count'] . "</p>";
    
    // عرض القوالب
    $query = "SELECT id, name, category, color_scheme FROM invoice_templates ORDER BY sort_order LIMIT 10";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($templates)) {
        echo "<h3>القوالب المتاحة:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'><th>ID</th><th>الاسم</th><th>الفئة</th><th>نظام الألوان</th></tr>";
        foreach ($templates as $template) {
            echo "<tr>";
            echo "<td>" . $template['id'] . "</td>";
            echo "<td>" . htmlspecialchars($template['name']) . "</td>";
            echo "<td>" . $template['category'] . "</td>";
            echo "<td>" . $template['color_scheme'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ حدث خطأ:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h2>الخطوات التالية:</h2>";
echo "<ol>";
echo "<li><a href='test_templates.php'>اختبار النظام</a></li>";
echo "<li><a href='login.php'>تسجيل الدخول</a></li>";
echo "<li><a href='templates.php'>صفحة القوالب</a></li>";
echo "<li><a href='dashboard.php'>لوحة التحكم</a></li>";
echo "</ol>";
?>
