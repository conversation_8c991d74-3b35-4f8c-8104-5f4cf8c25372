<?php
// نسخة مبسطة من صفحة القوالب للاختبار
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require_once 'includes/middleware.php';
    
    // التحقق من تسجيل الدخول
    requireAuth();
    
    $user = getCurrentUser();
    
    // الحصول على القوالب
    $query = "SELECT * FROM invoice_templates WHERE is_active = 1 ORDER BY sort_order ASC";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    die("خطأ: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قوالب الفواتير - نسخة مبسطة</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            direction: rtl;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .template-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .template-preview {
            height: 150px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            position: relative;
        }
        
        .template-name {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .template-description {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .template-category {
            display: inline-block;
            padding: 4px 12px;
            background: #e9ecef;
            border-radius: 15px;
            font-size: 12px;
            margin-bottom: 15px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.8;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-palette"></i> قوالب الفواتير</h1>
            <p>مرحباً <?php echo htmlspecialchars($user['full_name']); ?>، اختر القالب المناسب لفواتيرك</p>
            <p><a href="dashboard.php" class="btn btn-secondary">العودة للوحة التحكم</a></p>
        </div>
        
        <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> تم تطبيق القالب بنجاح!
            </div>
        <?php endif; ?>
        
        <?php if (empty($templates)): ?>
            <div class="alert alert-error">
                <h3>لا توجد قوالب متاحة</h3>
                <p>يبدو أن قوالب الفواتير لم يتم إعدادها بعد.</p>
                <a href="setup_templates.php" class="btn btn-primary">إعداد القوالب</a>
            </div>
        <?php else: ?>
            <div class="templates-grid">
                <?php foreach ($templates as $template): ?>
                    <div class="template-card">
                        <div class="template-preview" style="background: <?php 
                            $vars = json_decode($template['css_variables'], true);
                            echo $vars['accent'] ?? '#f8f9fa';
                        ?>;">
                            <div style="
                                background: <?php echo $vars['primary'] ?? '#2c3e50'; ?>;
                                color: white;
                                padding: 10px;
                                border-radius: 4px;
                                font-size: 12px;
                            ">
                                فاتورة نموذجية
                            </div>
                        </div>
                        
                        <h3 class="template-name"><?php echo htmlspecialchars($template['name']); ?></h3>
                        <p class="template-description"><?php echo htmlspecialchars($template['description']); ?></p>
                        <span class="template-category"><?php echo $template['category']; ?></span>
                        
                        <div>
                            <a href="template_preview.php?id=<?php echo $template['id']; ?>" 
                               target="_blank" class="btn btn-secondary">
                                <i class="fas fa-eye"></i> معاينة
                            </a>
                            
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="template_id" value="<?php echo $template['id']; ?>">
                                <button type="submit" name="set_template" class="btn btn-primary">
                                    <i class="fas fa-check"></i> استخدام
                                </button>
                            </form>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
    
    <?php
    // معالجة تطبيق القالب
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['set_template'])) {
        $templateId = $_POST['template_id'];
        
        try {
            // حفظ القالب للمستخدم
            $db->beginTransaction();
            
            // إزالة القالب الافتراضي الحالي
            $query = "DELETE FROM user_template_preferences WHERE user_id = ? AND is_default = 1";
            $stmt = $db->prepare($query);
            $stmt->execute([$user['id']]);
            
            // تعيين القالب الجديد
            $query = "INSERT INTO user_template_preferences (user_id, template_id, is_default) VALUES (?, ?, 1)";
            $stmt = $db->prepare($query);
            $stmt->execute([$user['id'], $templateId]);
            
            $db->commit();
            
            // إعادة توجيه مع رسالة نجاح
            header("Location: templates_simple.php?success=1");
            exit();
            
        } catch (Exception $e) {
            $db->rollback();
            echo "<div class='alert alert-error'>خطأ في حفظ القالب: " . $e->getMessage() . "</div>";
        }
    }
    ?>
</body>
</html>
