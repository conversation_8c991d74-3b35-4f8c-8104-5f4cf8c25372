<?php
// Middleware للحماية والتحقق من الصلاحيات

require_once __DIR__ . '/auth.php';

class Middleware {
    private $auth;

    public function __construct() {
        global $auth;
        $this->auth = $auth;
    }

    /**
     * التحقق من تسجيل الدخول
     */
    public function requireAuth($redirectTo = 'login.php') {
        if (!$this->auth->isLoggedIn()) {
            // حفظ الصفحة المطلوبة للعودة إليها بعد تسجيل الدخول
            $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
            
            // إعادة توجيه إلى صفحة تسجيل الدخول
            header("Location: $redirectTo");
            exit();
        }
    }

    /**
     * التحقق من عدم تسجيل الدخول (للصفحات مثل تسجيل الدخول والتسجيل)
     */
    public function requireGuest($redirectTo = 'dashboard.php') {
        if ($this->auth->isLoggedIn()) {
            header("Location: $redirectTo");
            exit();
        }
    }

    /**
     * التحقق من صلاحيات المدير
     */
    public function requireAdmin($redirectTo = 'dashboard.php') {
        $this->requireAuth();
        
        $user = $this->auth->getCurrentUser();
        if (!$user || $user['user_type'] !== 'admin') {
            header("Location: $redirectTo");
            exit();
        }
    }

    /**
     * التحقق من CSRF Token
     */
    public function validateCSRF($token) {
        if (!isset($_SESSION['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $token)) {
            return false;
        }
        return true;
    }

    /**
     * إنشاء CSRF Token
     */
    public function generateCSRF() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }

    /**
     * التحقق من ملكية المورد (للتأكد أن المستخدم يملك البيانات التي يحاول الوصول إليها)
     */
    public function checkResourceOwnership($resourceType, $resourceId) {
        $user = $this->auth->getCurrentUser();
        if (!$user) return false;

        // المدير يمكنه الوصول لجميع الموارد
        if ($user['user_type'] === 'admin') {
            return true;
        }

        global $db;

        switch ($resourceType) {
            case 'client':
                $query = "SELECT user_id FROM clients WHERE id = ?";
                break;
            case 'invoice':
                $query = "SELECT user_id FROM invoices WHERE id = ?";
                break;
            default:
                return false;
        }

        $stmt = $db->prepare($query);
        $stmt->execute([$resourceId]);
        $resource = $stmt->fetch(PDO::FETCH_ASSOC);

        return $resource && $resource['user_id'] == $user['id'];
    }

    /**
     * تصفية البيانات حسب المستخدم
     */
    public function addUserFilter($query, $tableName = null) {
        $user = $this->auth->getCurrentUser();
        if (!$user) return $query;

        // المدير يرى جميع البيانات
        if ($user['user_type'] === 'admin') {
            return $query;
        }

        // إضافة فلتر المستخدم
        $userColumn = $tableName ? "$tableName.user_id" : "user_id";
        
        if (stripos($query, 'WHERE') !== false) {
            $query .= " AND $userColumn = " . $user['id'];
        } else {
            $query .= " WHERE $userColumn = " . $user['id'];
        }

        return $query;
    }

    /**
     * تسجيل العمليات (Audit Log)
     */
    public function logActivity($action, $resourceType, $resourceId = null, $details = null) {
        $user = $this->auth->getCurrentUser();
        if (!$user) return;

        try {
            global $db;
            
            $query = "INSERT INTO activity_logs (user_id, action, resource_type, resource_id, details, ip_address, user_agent) 
                      VALUES (?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $db->prepare($query);
            $stmt->execute([
                $user['id'],
                $action,
                $resourceType,
                $resourceId,
                $details ? json_encode($details) : null,
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
        } catch (Exception $e) {
            Config::logError("Activity log error: " . $e->getMessage());
        }
    }

    /**
     * التحقق من معدل الطلبات (Rate Limiting)
     */
    public function checkRateLimit($action, $maxAttempts = 10, $timeWindow = 60) {
        $user = $this->auth->getCurrentUser();
        if (!$user) return true; // السماح للضيوف بمعدل محدود

        $key = "rate_limit_{$user['id']}_{$action}";
        
        if (!isset($_SESSION[$key])) {
            $_SESSION[$key] = ['count' => 0, 'reset_time' => time() + $timeWindow];
        }

        $rateData = $_SESSION[$key];

        // إعادة تعيين العداد إذا انتهت النافزة الزمنية
        if (time() > $rateData['reset_time']) {
            $_SESSION[$key] = ['count' => 1, 'reset_time' => time() + $timeWindow];
            return true;
        }

        // التحقق من تجاوز الحد الأقصى
        if ($rateData['count'] >= $maxAttempts) {
            return false;
        }

        // زيادة العداد
        $_SESSION[$key]['count']++;
        return true;
    }

    /**
     * تنظيف البيانات المدخلة
     */
    public function sanitizeInput($data) {
        if (is_array($data)) {
            return array_map([$this, 'sanitizeInput'], $data);
        }
        
        return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
    }

    /**
     * التحقق من صحة البريد الإلكتروني
     */
    public function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * التحقق من قوة كلمة المرور
     */
    public function validatePassword($password) {
        // كلمة المرور يجب أن تكون 8 أحرف على الأقل
        if (strlen($password) < 8) {
            return ['valid' => false, 'message' => 'كلمة المرور يجب أن تكون 8 أحرف على الأقل'];
        }

        // يجب أن تحتوي على حرف كبير وصغير ورقم
        if (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/', $password)) {
            return ['valid' => false, 'message' => 'كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم'];
        }

        return ['valid' => true, 'message' => 'كلمة المرور قوية'];
    }

    /**
     * إنشاء رمز تأكيد عشوائي
     */
    public function generateVerificationToken() {
        return bin2hex(random_bytes(32));
    }

    /**
     * تشفير البيانات الحساسة
     */
    public function encryptData($data, $key = null) {
        if (!$key) {
            $key = hash('sha256', 'invoice_system_secret_key', true);
        }
        
        $iv = random_bytes(16);
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);
        
        return base64_encode($iv . $encrypted);
    }

    /**
     * فك تشفير البيانات
     */
    public function decryptData($encryptedData, $key = null) {
        if (!$key) {
            $key = hash('sha256', 'invoice_system_secret_key', true);
        }
        
        $data = base64_decode($encryptedData);
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        
        return openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
    }

    /**
     * التحقق من الامتدادات المسموحة للملفات
     */
    public function validateFileExtension($filename, $allowedExtensions) {
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        return in_array($extension, $allowedExtensions);
    }

    /**
     * التحقق من نوع MIME للملف
     */
    public function validateMimeType($filepath, $allowedTypes) {
        $mimeType = mime_content_type($filepath);
        return in_array($mimeType, $allowedTypes);
    }

    /**
     * إنشاء اسم ملف آمن
     */
    public function generateSafeFilename($originalName) {
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $basename = pathinfo($originalName, PATHINFO_FILENAME);
        
        // تنظيف اسم الملف
        $basename = preg_replace('/[^a-zA-Z0-9\-_\u0600-\u06FF]/', '_', $basename);
        $basename = substr($basename, 0, 50); // تحديد الطول
        
        return $basename . '_' . time() . '_' . uniqid() . '.' . $extension;
    }

    /**
     * التحقق من عنوان IP
     */
    public function validateIP($ip) {
        return filter_var($ip, FILTER_VALIDATE_IP) !== false;
    }

    /**
     * الحصول على عنوان IP الحقيقي للمستخدم
     */
    public function getRealIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);
                
                if ($this->validateIP($ip)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
}

// إنشاء مثيل من Middleware
$middleware = new Middleware();

// دوال مساعدة سريعة
function requireAuth($redirectTo = 'login.php') {
    global $middleware;
    $middleware->requireAuth($redirectTo);
}

function requireGuest($redirectTo = 'dashboard.php') {
    global $middleware;
    $middleware->requireGuest($redirectTo);
}

function requireAdmin($redirectTo = 'dashboard.php') {
    global $middleware;
    $middleware->requireAdmin($redirectTo);
}

function csrf_token() {
    global $middleware;
    return $middleware->generateCSRF();
}

function csrf_field() {
    return '<input type="hidden" name="csrf_token" value="' . csrf_token() . '">';
}

function getCurrentUser() {
    global $auth;
    return $auth->getCurrentUser();
}

function isLoggedIn() {
    global $auth;
    return $auth->isLoggedIn();
}
?>
