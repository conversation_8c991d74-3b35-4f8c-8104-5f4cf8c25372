<?php
// ملف اختبار للتحقق من مشاكل القوالب
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>اختبار نظام القوالب</h1>";

try {
    echo "<h2>1. اختبار الاتصال بقاعدة البيانات</h2>";
    require_once 'config/database.php';
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح<br>";
    
    echo "<h2>2. اختبار وجود جدول القوالب</h2>";
    $query = "SHOW TABLES LIKE 'invoice_templates'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $result = $stmt->fetch();
    
    if ($result) {
        echo "✅ جدول invoice_templates موجود<br>";
        
        // اختبار عدد القوالب
        $query = "SELECT COUNT(*) as count FROM invoice_templates";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $count = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "📊 عدد القوالب: " . $count['count'] . "<br>";
        
        // عرض أول 5 قوالب
        $query = "SELECT id, name, category, color_scheme FROM invoice_templates LIMIT 5";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>أول 5 قوالب:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>الاسم</th><th>الفئة</th><th>نظام الألوان</th></tr>";
        foreach ($templates as $template) {
            echo "<tr>";
            echo "<td>" . $template['id'] . "</td>";
            echo "<td>" . htmlspecialchars($template['name']) . "</td>";
            echo "<td>" . $template['category'] . "</td>";
            echo "<td>" . $template['color_scheme'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } else {
        echo "❌ جدول invoice_templates غير موجود<br>";
        echo "<p>يجب تشغيل ملف database.sql لإنشاء الجداول</p>";
    }
    
    echo "<h2>3. اختبار جدول تفضيلات المستخدمين</h2>";
    $query = "SHOW TABLES LIKE 'user_template_preferences'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $result = $stmt->fetch();
    
    if ($result) {
        echo "✅ جدول user_template_preferences موجود<br>";
    } else {
        echo "❌ جدول user_template_preferences غير موجود<br>";
    }
    
    echo "<h2>4. اختبار نظام المصادقة</h2>";
    require_once 'includes/middleware.php';
    
    if (isLoggedIn()) {
        $user = getCurrentUser();
        echo "✅ المستخدم مسجل الدخول: " . htmlspecialchars($user['full_name']) . "<br>";
    } else {
        echo "❌ المستخدم غير مسجل الدخول<br>";
        echo "<a href='login.php'>تسجيل الدخول</a>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<h3>تفاصيل الخطأ:</h3>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<h2>5. روابط مفيدة</h2>";
echo "<a href='login.php'>تسجيل الدخول</a> | ";
echo "<a href='dashboard.php'>لوحة التحكم</a> | ";
echo "<a href='templates.php'>صفحة القوالب</a>";
?>
