<?php
require_once 'includes/middleware.php';

// التحقق من تسجيل الدخول
requireAuth();

$user = getCurrentUser();
$error = '';
$success = '';

// معالجة تحديث الملف الشخصي
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_profile') {
        $result = updateProfile($_POST);
        if ($result['success']) {
            $success = $result['message'];
            // إعادة تحميل بيانات المستخدم
            $user = getCurrentUser();
        } else {
            $error = $result['message'];
        }
    } elseif ($action === 'change_password') {
        $result = changePassword($_POST);
        if ($result['success']) {
            $success = $result['message'];
        } else {
            $error = $result['message'];
        }
    }
}

function updateProfile($data) {
    global $db, $middleware, $user;
    
    // التحقق من CSRF Token
    if (!$middleware->validateCSRF($data['csrf_token'] ?? '')) {
        return ['success' => false, 'message' => 'رمز الأمان غير صحيح'];
    }
    
    try {
        $query = "UPDATE users SET 
                  full_name = ?, 
                  company_name = ?, 
                  phone = ?, 
                  address = ?, 
                  email = ?
                  WHERE id = ?";
        
        $stmt = $db->prepare($query);
        $stmt->execute([
            trim($data['full_name']),
            trim($data['company_name']),
            trim($data['phone']),
            trim($data['address']),
            trim($data['email']),
            $user['id']
        ]);
        
        return ['success' => true, 'message' => 'تم تحديث الملف الشخصي بنجاح'];
        
    } catch (Exception $e) {
        Config::logError("Profile update error: " . $e->getMessage());
        return ['success' => false, 'message' => 'حدث خطأ أثناء تحديث الملف الشخصي'];
    }
}

function changePassword($data) {
    global $db, $middleware, $user;
    
    // التحقق من CSRF Token
    if (!$middleware->validateCSRF($data['csrf_token'] ?? '')) {
        return ['success' => false, 'message' => 'رمز الأمان غير صحيح'];
    }
    
    $currentPassword = $data['current_password'] ?? '';
    $newPassword = $data['new_password'] ?? '';
    $confirmPassword = $data['confirm_password'] ?? '';
    
    // التحقق من كلمة المرور الحالية
    if (!password_verify($currentPassword, $user['password_hash'])) {
        return ['success' => false, 'message' => 'كلمة المرور الحالية غير صحيحة'];
    }
    
    // التحقق من تطابق كلمة المرور الجديدة
    if ($newPassword !== $confirmPassword) {
        return ['success' => false, 'message' => 'كلمة المرور الجديدة وتأكيدها غير متطابقتين'];
    }
    
    // التحقق من قوة كلمة المرور
    $passwordValidation = $middleware->validatePassword($newPassword);
    if (!$passwordValidation['valid']) {
        return ['success' => false, 'message' => $passwordValidation['message']];
    }
    
    try {
        $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);
        
        $query = "UPDATE users SET password_hash = ? WHERE id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$passwordHash, $user['id']]);
        
        return ['success' => true, 'message' => 'تم تغيير كلمة المرور بنجاح'];
        
    } catch (Exception $e) {
        Config::logError("Password change error: " . $e->getMessage());
        return ['success' => false, 'message' => 'حدث خطأ أثناء تغيير كلمة المرور'];
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الملف الشخصي - نظام إنشاء الفواتير</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        .profile-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .profile-header {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
        }

        .profile-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 36px;
            color: white;
        }

        .profile-content {
            display: grid;
            grid-template-columns: 1fr;
            gap: 30px;
        }

        .profile-section {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f8f9fa;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #2c3e50;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn-update {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-update:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .navbar {
            background: white;
            padding: 15px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .navbar-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .navbar-brand {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            text-decoration: none;
        }

        .navbar-nav {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .nav-link {
            color: #6c757d;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            background-color: #f8f9fa;
            color: #2c3e50;
        }

        .user-menu {
            position: relative;
            display: inline-block;
        }

        .user-toggle {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            background: #f8f9fa;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            color: #2c3e50;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 6px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            min-width: 180px;
            z-index: 1000;
        }

        .dropdown-item {
            display: block;
            padding: 10px 15px;
            color: #2c3e50;
            text-decoration: none;
            transition: background-color 0.3s ease;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar">
        <div class="navbar-content">
            <a href="dashboard.php" class="navbar-brand">
                <i class="fas fa-file-invoice"></i> نظام الفواتير
            </a>
            
            <div class="navbar-nav">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-home"></i> الرئيسية
                </a>
                <a href="index.php" class="nav-link">
                    <i class="fas fa-plus"></i> فاتورة جديدة
                </a>
                <a href="invoices.php" class="nav-link">
                    <i class="fas fa-file-invoice-dollar"></i> الفواتير
                </a>
                
                <div class="user-menu">
                    <button class="user-toggle" onclick="toggleUserMenu()">
                        <i class="fas fa-user"></i>
                        <?php echo htmlspecialchars($user['full_name']); ?>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div id="userDropdown" class="dropdown-menu" style="display: none;">
                        <a href="profile.php" class="dropdown-item">
                            <i class="fas fa-user-edit"></i> الملف الشخصي
                        </a>
                        <a href="logout.php" class="dropdown-item">
                            <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="profile-container">
        <!-- رأس الملف الشخصي -->
        <div class="profile-header">
            <div class="profile-avatar">
                <?php echo strtoupper(substr($user['full_name'], 0, 1)); ?>
            </div>
            <h1><?php echo htmlspecialchars($user['full_name']); ?></h1>
            <p><?php echo htmlspecialchars($user['email']); ?></p>
            <?php if ($user['company_name']): ?>
                <p><i class="fas fa-building"></i> <?php echo htmlspecialchars($user['company_name']); ?></p>
            <?php endif; ?>
        </div>

        <?php if ($error): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success); ?>
            </div>
        <?php endif; ?>

        <div class="profile-content">
            <!-- تحديث المعلومات الشخصية -->
            <div class="profile-section">
                <h2 class="section-title">
                    <i class="fas fa-user-edit"></i> المعلومات الشخصية
                </h2>
                
                <form method="POST" action="">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="action" value="update_profile">
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="full_name">الاسم الكامل</label>
                            <input type="text" id="full_name" name="full_name" class="form-control" 
                                   value="<?php echo htmlspecialchars($user['full_name']); ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="company_name">اسم الشركة</label>
                            <input type="text" id="company_name" name="company_name" class="form-control" 
                                   value="<?php echo htmlspecialchars($user['company_name'] ?? ''); ?>">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">البريد الإلكتروني</label>
                        <input type="email" id="email" name="email" class="form-control" 
                               value="<?php echo htmlspecialchars($user['email']); ?>" required>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="phone">رقم الهاتف</label>
                            <input type="tel" id="phone" name="phone" class="form-control" 
                                   value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="address">العنوان</label>
                            <input type="text" id="address" name="address" class="form-control" 
                                   value="<?php echo htmlspecialchars($user['address'] ?? ''); ?>">
                        </div>
                    </div>
                    
                    <button type="submit" class="btn-update">
                        <i class="fas fa-save"></i> حفظ التغييرات
                    </button>
                </form>
            </div>

            <!-- تغيير كلمة المرور -->
            <div class="profile-section">
                <h2 class="section-title">
                    <i class="fas fa-lock"></i> تغيير كلمة المرور
                </h2>
                
                <form method="POST" action="">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="action" value="change_password">
                    
                    <div class="form-group">
                        <label for="current_password">كلمة المرور الحالية</label>
                        <input type="password" id="current_password" name="current_password" class="form-control" required>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="new_password">كلمة المرور الجديدة</label>
                            <input type="password" id="new_password" name="new_password" class="form-control" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="confirm_password">تأكيد كلمة المرور</label>
                            <input type="password" id="confirm_password" name="confirm_password" class="form-control" required>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn-update">
                        <i class="fas fa-key"></i> تغيير كلمة المرور
                    </button>
                </form>
            </div>
        </div>
    </div>

    <script>
        function toggleUserMenu() {
            const dropdown = document.getElementById('userDropdown');
            dropdown.style.display = dropdown.style.display === 'none' ? 'block' : 'none';
        }

        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', function(event) {
            const userMenu = document.querySelector('.user-menu');
            const dropdown = document.getElementById('userDropdown');
            
            if (!userMenu.contains(event.target)) {
                dropdown.style.display = 'none';
            }
        });

        // التحقق من تطابق كلمة المرور
        document.getElementById('confirm_password').addEventListener('input', function() {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = this.value;
            
            if (newPassword !== confirmPassword) {
                this.setCustomValidity('كلمة المرور غير متطابقة');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
