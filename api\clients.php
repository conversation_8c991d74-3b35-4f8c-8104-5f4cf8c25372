<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

class ClientManager {
    private $conn;
    private $table_name = "clients";

    public function __construct($db) {
        $this->conn = $db;
    }

    // Get all clients
    public function getAll() {
        $query = "SELECT * FROM " . $this->table_name . " ORDER BY name ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        
        $clients = array();
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $clients[] = $row;
        }
        
        return $clients;
    }

    // Get client by ID
    public function getById($id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = ? LIMIT 0,1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $id);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Create new client
    public function create($data) {
        $query = "INSERT INTO " . $this->table_name . " 
                  SET name=:name, email=:email, phone=:phone, address=:address";
        
        $stmt = $this->conn->prepare($query);
        
        // Sanitize data
        $data['name'] = htmlspecialchars(strip_tags($data['name']));
        $data['email'] = htmlspecialchars(strip_tags($data['email']));
        $data['phone'] = htmlspecialchars(strip_tags($data['phone']));
        $data['address'] = htmlspecialchars(strip_tags($data['address']));
        
        // Bind data
        $stmt->bindParam(":name", $data['name']);
        $stmt->bindParam(":email", $data['email']);
        $stmt->bindParam(":phone", $data['phone']);
        $stmt->bindParam(":address", $data['address']);
        
        if($stmt->execute()) {
            return $this->conn->lastInsertId();
        }
        
        return false;
    }

    // Update client
    public function update($id, $data) {
        $query = "UPDATE " . $this->table_name . " 
                  SET name=:name, email=:email, phone=:phone, address=:address 
                  WHERE id = :id";
        
        $stmt = $this->conn->prepare($query);
        
        // Sanitize data
        $data['name'] = htmlspecialchars(strip_tags($data['name']));
        $data['email'] = htmlspecialchars(strip_tags($data['email']));
        $data['phone'] = htmlspecialchars(strip_tags($data['phone']));
        $data['address'] = htmlspecialchars(strip_tags($data['address']));
        
        // Bind data
        $stmt->bindParam(":id", $id);
        $stmt->bindParam(":name", $data['name']);
        $stmt->bindParam(":email", $data['email']);
        $stmt->bindParam(":phone", $data['phone']);
        $stmt->bindParam(":address", $data['address']);
        
        return $stmt->execute();
    }

    // Delete client
    public function delete($id) {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $id);
        
        return $stmt->execute();
    }

    // Search clients
    public function search($keyword) {
        $query = "SELECT * FROM " . $this->table_name . " 
                  WHERE name LIKE ? OR email LIKE ? OR phone LIKE ? 
                  ORDER BY name ASC";
        
        $stmt = $this->conn->prepare($query);
        $keyword = "%{$keyword}%";
        $stmt->bindParam(1, $keyword);
        $stmt->bindParam(2, $keyword);
        $stmt->bindParam(3, $keyword);
        $stmt->execute();
        
        $clients = array();
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $clients[] = $row;
        }
        
        return $clients;
    }
}

// Handle API requests
$method = $_SERVER['REQUEST_METHOD'];
$clientManager = new ClientManager($db);

switch($method) {
    case 'GET':
        if (isset($_GET['id'])) {
            // Get specific client
            $client = $clientManager->getById($_GET['id']);
            if ($client) {
                echo json_encode(['success' => true, 'data' => $client]);
            } else {
                echo json_encode(['success' => false, 'message' => 'العميل غير موجود']);
            }
        } elseif (isset($_GET['search'])) {
            // Search clients
            $clients = $clientManager->search($_GET['search']);
            echo json_encode(['success' => true, 'data' => $clients]);
        } else {
            // Get all clients
            $clients = $clientManager->getAll();
            echo json_encode(['success' => true, 'data' => $clients]);
        }
        break;
        
    case 'POST':
        // Create new client
        $data = json_decode(file_get_contents("php://input"), true);
        
        if (empty($data['name'])) {
            echo json_encode(['success' => false, 'message' => 'اسم العميل مطلوب']);
            break;
        }
        
        $clientId = $clientManager->create($data);
        if ($clientId) {
            $client = $clientManager->getById($clientId);
            echo json_encode(['success' => true, 'message' => 'تم إنشاء العميل بنجاح', 'data' => $client]);
        } else {
            echo json_encode(['success' => false, 'message' => 'فشل في إنشاء العميل']);
        }
        break;
        
    case 'PUT':
        // Update client
        $data = json_decode(file_get_contents("php://input"), true);
        
        if (empty($data['id']) || empty($data['name'])) {
            echo json_encode(['success' => false, 'message' => 'معرف العميل والاسم مطلوبان']);
            break;
        }
        
        if ($clientManager->update($data['id'], $data)) {
            $client = $clientManager->getById($data['id']);
            echo json_encode(['success' => true, 'message' => 'تم تحديث العميل بنجاح', 'data' => $client]);
        } else {
            echo json_encode(['success' => false, 'message' => 'فشل في تحديث العميل']);
        }
        break;
        
    case 'DELETE':
        // Delete client
        $data = json_decode(file_get_contents("php://input"), true);
        
        if (empty($data['id'])) {
            echo json_encode(['success' => false, 'message' => 'معرف العميل مطلوب']);
            break;
        }
        
        if ($clientManager->delete($data['id'])) {
            echo json_encode(['success' => true, 'message' => 'تم حذف العميل بنجاح']);
        } else {
            echo json_encode(['success' => false, 'message' => 'فشل في حذف العميل']);
        }
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'طريقة غير مدعومة']);
        break;
}
?>
