<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../includes/middleware.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً']);
    exit();
}

class ClientManager {
    private $conn;
    private $table_name = "clients";

    public function __construct($db) {
        $this->conn = $db;
    }

    // Get all clients for current user
    public function getAll($userId) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE user_id = ? ORDER BY name ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$userId]);

        $clients = array();
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $clients[] = $row;
        }

        return $clients;
    }

    // Get client by ID (with user ownership check)
    public function getById($id, $userId) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = ? AND user_id = ? LIMIT 0,1";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$id, $userId]);

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Create new client
    public function create($data, $userId) {
        $query = "INSERT INTO " . $this->table_name . "
                  SET user_id=:user_id, name=:name, email=:email, phone=:phone, address=:address";

        $stmt = $this->conn->prepare($query);

        // Sanitize data
        $data['name'] = htmlspecialchars(strip_tags($data['name']));
        $data['email'] = htmlspecialchars(strip_tags($data['email']));
        $data['phone'] = htmlspecialchars(strip_tags($data['phone']));
        $data['address'] = htmlspecialchars(strip_tags($data['address']));

        // Bind data
        $stmt->bindParam(":user_id", $userId);
        $stmt->bindParam(":name", $data['name']);
        $stmt->bindParam(":email", $data['email']);
        $stmt->bindParam(":phone", $data['phone']);
        $stmt->bindParam(":address", $data['address']);

        if($stmt->execute()) {
            return $this->conn->lastInsertId();
        }

        return false;
    }

    // Update client (with user ownership check)
    public function update($id, $data, $userId) {
        $query = "UPDATE " . $this->table_name . "
                  SET name=:name, email=:email, phone=:phone, address=:address
                  WHERE id = :id AND user_id = :user_id";

        $stmt = $this->conn->prepare($query);

        // Sanitize data
        $data['name'] = htmlspecialchars(strip_tags($data['name']));
        $data['email'] = htmlspecialchars(strip_tags($data['email']));
        $data['phone'] = htmlspecialchars(strip_tags($data['phone']));
        $data['address'] = htmlspecialchars(strip_tags($data['address']));

        // Bind data
        $stmt->bindParam(":id", $id);
        $stmt->bindParam(":user_id", $userId);
        $stmt->bindParam(":name", $data['name']);
        $stmt->bindParam(":email", $data['email']);
        $stmt->bindParam(":phone", $data['phone']);
        $stmt->bindParam(":address", $data['address']);

        return $stmt->execute();
    }

    // Delete client (with user ownership check)
    public function delete($id, $userId) {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = ? AND user_id = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$id, $userId]);

        return $stmt->rowCount() > 0;
    }

    // Search clients for current user
    public function search($keyword, $userId) {
        $query = "SELECT * FROM " . $this->table_name . "
                  WHERE user_id = ? AND (name LIKE ? OR email LIKE ? OR phone LIKE ?)
                  ORDER BY name ASC";

        $stmt = $this->conn->prepare($query);
        $keyword = "%{$keyword}%";
        $stmt->execute([$userId, $keyword, $keyword, $keyword]);

        $clients = array();
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $clients[] = $row;
        }

        return $clients;
    }
}

// Handle API requests
$method = $_SERVER['REQUEST_METHOD'];
$clientManager = new ClientManager($db);
$currentUser = getCurrentUser();

switch($method) {
    case 'GET':
        if (isset($_GET['id'])) {
            // Get specific client
            $client = $clientManager->getById($_GET['id'], $currentUser['id']);
            if ($client) {
                echo json_encode(['success' => true, 'data' => $client]);
            } else {
                echo json_encode(['success' => false, 'message' => 'العميل غير موجود']);
            }
        } elseif (isset($_GET['search'])) {
            // Search clients
            $clients = $clientManager->search($_GET['search'], $currentUser['id']);
            echo json_encode(['success' => true, 'data' => $clients]);
        } else {
            // Get all clients
            $clients = $clientManager->getAll($currentUser['id']);
            echo json_encode(['success' => true, 'data' => $clients]);
        }
        break;
        
    case 'POST':
        // Create new client
        $data = json_decode(file_get_contents("php://input"), true);

        if (empty($data['name'])) {
            echo json_encode(['success' => false, 'message' => 'اسم العميل مطلوب']);
            break;
        }

        $clientId = $clientManager->create($data, $currentUser['id']);
        if ($clientId) {
            $client = $clientManager->getById($clientId, $currentUser['id']);
            echo json_encode(['success' => true, 'message' => 'تم إنشاء العميل بنجاح', 'data' => $client]);
        } else {
            echo json_encode(['success' => false, 'message' => 'فشل في إنشاء العميل']);
        }
        break;
        
    case 'PUT':
        // Update client
        $data = json_decode(file_get_contents("php://input"), true);

        if (empty($data['id']) || empty($data['name'])) {
            echo json_encode(['success' => false, 'message' => 'معرف العميل والاسم مطلوبان']);
            break;
        }

        if ($clientManager->update($data['id'], $data, $currentUser['id'])) {
            $client = $clientManager->getById($data['id'], $currentUser['id']);
            echo json_encode(['success' => true, 'message' => 'تم تحديث العميل بنجاح', 'data' => $client]);
        } else {
            echo json_encode(['success' => false, 'message' => 'فشل في تحديث العميل أو العميل غير موجود']);
        }
        break;

    case 'DELETE':
        // Delete client
        $data = json_decode(file_get_contents("php://input"), true);

        if (empty($data['id'])) {
            echo json_encode(['success' => false, 'message' => 'معرف العميل مطلوب']);
            break;
        }

        if ($clientManager->delete($data['id'], $currentUser['id'])) {
            echo json_encode(['success' => true, 'message' => 'تم حذف العميل بنجاح']);
        } else {
            echo json_encode(['success' => false, 'message' => 'فشل في حذف العميل أو العميل غير موجود']);
        }
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => 'طريقة غير مدعومة']);
        break;
}
?>
