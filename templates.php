<?php
require_once 'includes/middleware.php';

// التحقق من تسجيل الدخول
requireAuth();

$user = getCurrentUser();

// الحصول على جميع القوالب
function getTemplates($category = null, $search = null) {
    global $db;
    
    $query = "SELECT * FROM invoice_templates WHERE is_active = 1";
    $params = [];
    
    if ($category) {
        $query .= " AND category = ?";
        $params[] = $category;
    }
    
    if ($search) {
        $query .= " AND (name LIKE ? OR description LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    $query .= " ORDER BY sort_order ASC, name ASC";
    
    $stmt = $db->prepare($query);
    $stmt->execute($params);
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// الحصول على القالب المفضل للمستخدم
function getUserDefaultTemplate($userId) {
    global $db;
    
    $query = "SELECT t.* FROM invoice_templates t 
              JOIN user_template_preferences utp ON t.id = utp.template_id 
              WHERE utp.user_id = ? AND utp.is_default = 1";
    
    $stmt = $db->prepare($query);
    $stmt->execute([$userId]);
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

// تعيين القالب الافتراضي للمستخدم
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['set_default_template'])) {
    $templateId = $_POST['template_id'];
    
    try {
        $db->beginTransaction();
        
        // إزالة القالب الافتراضي الحالي
        $query = "DELETE FROM user_template_preferences WHERE user_id = ? AND is_default = 1";
        $stmt = $db->prepare($query);
        $stmt->execute([$user['id']]);
        
        // تعيين القالب الجديد كافتراضي
        $query = "INSERT INTO user_template_preferences (user_id, template_id, is_default) 
                  VALUES (?, ?, 1) 
                  ON DUPLICATE KEY UPDATE is_default = 1";
        $stmt = $db->prepare($query);
        $stmt->execute([$user['id'], $templateId]);
        
        $db->commit();
        
        // حفظ في الجلسة أيضاً
        $_SESSION['selected_template'] = $templateId;
        
        $success = "تم تعيين القالب كافتراضي بنجاح";
    } catch (Exception $e) {
        $db->rollback();
        $error = "حدث خطأ أثناء تعيين القالب";
    }
}

$category = $_GET['category'] ?? null;
$search = $_GET['search'] ?? null;
$templates = getTemplates($category, $search);
$userDefaultTemplate = getUserDefaultTemplate($user['id']);

$categories = [
    'classic' => 'كلاسيكي',
    'modern' => 'عصري',
    'creative' => 'إبداعي',
    'minimal' => 'مينيمال',
    'professional' => 'احترافي'
];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قوالب الفواتير - نظام إنشاء الفواتير</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        .templates-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .templates-header {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .templates-header h1 {
            font-size: 28px;
            color: #2c3e50;
            margin: 0 0 10px 0;
        }

        .templates-header p {
            color: #6c757d;
            margin: 0 0 20px 0;
        }

        .templates-filters {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-box {
            flex: 1;
            min-width: 300px;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 12px 45px 12px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 14px;
        }

        .search-btn {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
        }

        .category-filters {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .category-btn {
            padding: 8px 16px;
            border: 2px solid #e1e8ed;
            background: white;
            color: #6c757d;
            border-radius: 20px;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .category-btn:hover,
        .category-btn.active {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }

        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .template-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            position: relative;
        }

        .template-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .template-preview {
            height: 200px;
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .template-preview-content {
            width: 100%;
            height: 100%;
            transform: scale(0.3);
            transform-origin: top right;
            position: absolute;
            top: 0;
            right: 0;
        }

        .template-info {
            padding: 20px;
        }

        .template-name {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .template-description {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .template-category {
            display: inline-block;
            padding: 4px 12px;
            background: #f8f9fa;
            color: #6c757d;
            border-radius: 15px;
            font-size: 12px;
            margin-bottom: 15px;
        }

        .template-actions {
            display: flex;
            gap: 10px;
        }

        .btn-preview {
            flex: 1;
            padding: 10px;
            background: #f8f9fa;
            color: #2c3e50;
            border: 1px solid #e1e8ed;
            border-radius: 6px;
            text-decoration: none;
            text-align: center;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn-preview:hover {
            background: #e9ecef;
        }

        .btn-use-template {
            flex: 2;
            padding: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-use-template:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .default-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
        }

        .modal-content {
            background-color: white;
            margin: 2% auto;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 1000px;
            max-height: 90vh;
            overflow: hidden;
            position: relative;
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #e1e8ed;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-body {
            padding: 20px;
            max-height: 70vh;
            overflow-y: auto;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .preview-iframe {
            width: 100%;
            height: 600px;
            border: none;
            border-radius: 8px;
        }

        .navbar {
            background: white;
            padding: 15px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .navbar-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .navbar-brand {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            text-decoration: none;
        }

        .navbar-nav {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .nav-link {
            color: #6c757d;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .nav-link:hover, .nav-link.active {
            background-color: #f8f9fa;
            color: #2c3e50;
        }

        .user-menu {
            position: relative;
            display: inline-block;
        }

        .user-toggle {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            background: #f8f9fa;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            color: #2c3e50;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 6px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            min-width: 180px;
            z-index: 1000;
        }

        .dropdown-item {
            display: block;
            padding: 10px 15px;
            color: #2c3e50;
            text-decoration: none;
            transition: background-color 0.3s ease;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        @media (max-width: 768px) {
            .templates-filters {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-box {
                min-width: auto;
            }
            
            .templates-grid {
                grid-template-columns: 1fr;
            }
            
            .modal-content {
                width: 95%;
                margin: 5% auto;
            }
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar">
        <div class="navbar-content">
            <a href="dashboard.php" class="navbar-brand">
                <i class="fas fa-file-invoice"></i> نظام الفواتير
            </a>
            
            <div class="navbar-nav">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-home"></i> الرئيسية
                </a>
                <a href="index.php" class="nav-link">
                    <i class="fas fa-plus"></i> فاتورة جديدة
                </a>
                <a href="invoices.php" class="nav-link">
                    <i class="fas fa-file-invoice-dollar"></i> الفواتير
                </a>
                <a href="templates.php" class="nav-link active">
                    <i class="fas fa-palette"></i> القوالب
                </a>
                
                <div class="user-menu">
                    <button class="user-toggle" onclick="toggleUserMenu()">
                        <i class="fas fa-user"></i>
                        <?php echo htmlspecialchars($user['full_name']); ?>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div id="userDropdown" class="dropdown-menu" style="display: none;">
                        <a href="profile.php" class="dropdown-item">
                            <i class="fas fa-user-edit"></i> الملف الشخصي
                        </a>
                        <a href="logout.php" class="dropdown-item">
                            <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="templates-container">
        <!-- رأس الصفحة -->
        <div class="templates-header">
            <h1><i class="fas fa-palette"></i> قوالب الفواتير</h1>
            <p>اختر القالب المناسب لفواتيرك من مجموعة متنوعة من التصاميم الاحترافية</p>
            
            <?php if ($userDefaultTemplate): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    القالب الافتراضي الحالي: <strong><?php echo htmlspecialchars($userDefaultTemplate['name']); ?></strong>
                </div>
            <?php endif; ?>
            
            <!-- فلاتر البحث والتصنيف -->
            <div class="templates-filters">
                <div class="search-box">
                    <form method="GET" action="">
                        <input type="text" name="search" class="search-input" 
                               placeholder="البحث في القوالب..." 
                               value="<?php echo htmlspecialchars($search ?? ''); ?>">
                        <button type="submit" class="search-btn">
                            <i class="fas fa-search"></i>
                        </button>
                        <?php if ($category): ?>
                            <input type="hidden" name="category" value="<?php echo htmlspecialchars($category); ?>">
                        <?php endif; ?>
                    </form>
                </div>
                
                <div class="category-filters">
                    <a href="templates.php" class="category-btn <?php echo !$category ? 'active' : ''; ?>">
                        الكل
                    </a>
                    <?php foreach ($categories as $key => $name): ?>
                        <a href="?category=<?php echo $key; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>" 
                           class="category-btn <?php echo $category === $key ? 'active' : ''; ?>">
                            <?php echo $name; ?>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- شبكة القوالب -->
        <div class="templates-grid">
            <?php foreach ($templates as $template): ?>
                <div class="template-card">
                    <?php if ($userDefaultTemplate && $userDefaultTemplate['id'] == $template['id']): ?>
                        <div class="default-badge">افتراضي</div>
                    <?php endif; ?>
                    
                    <div class="template-preview" onclick="previewTemplate(<?php echo $template['id']; ?>)">
                        <div class="template-preview-content" id="preview-<?php echo $template['id']; ?>">
                            <!-- سيتم تحميل معاينة القالب هنا -->
                        </div>
                    </div>
                    
                    <div class="template-info">
                        <h3 class="template-name"><?php echo htmlspecialchars($template['name']); ?></h3>
                        <p class="template-description"><?php echo htmlspecialchars($template['description']); ?></p>
                        <span class="template-category"><?php echo $categories[$template['category']]; ?></span>
                        
                        <div class="template-actions">
                            <button class="btn-preview" onclick="previewTemplate(<?php echo $template['id']; ?>)">
                                <i class="fas fa-eye"></i> معاينة
                            </button>
                            <form method="POST" style="flex: 2;">
                                <input type="hidden" name="template_id" value="<?php echo $template['id']; ?>">
                                <button type="submit" name="set_default_template" class="btn-use-template">
                                    <i class="fas fa-check"></i> استخدام هذا القالب
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <?php if (empty($templates)): ?>
            <div style="text-align: center; padding: 60px; color: #6c757d;">
                <i class="fas fa-palette" style="font-size: 48px; margin-bottom: 20px; opacity: 0.3;"></i>
                <h3>لا توجد قوالب</h3>
                <p>لم يتم العثور على قوالب تطابق معايير البحث</p>
                <a href="templates.php" class="btn btn-primary">عرض جميع القوالب</a>
            </div>
        <?php endif; ?>
    </div>

    <!-- نافذة المعاينة -->
    <div id="previewModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="previewTitle">معاينة القالب</h2>
                <span class="close" onclick="closePreview()">&times;</span>
            </div>
            <div class="modal-body">
                <iframe id="previewFrame" class="preview-iframe" src=""></iframe>
            </div>
        </div>
    </div>

    <script>
        function toggleUserMenu() {
            const dropdown = document.getElementById('userDropdown');
            dropdown.style.display = dropdown.style.display === 'none' ? 'block' : 'none';
        }

        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', function(event) {
            const userMenu = document.querySelector('.user-menu');
            const dropdown = document.getElementById('userDropdown');
            
            if (!userMenu.contains(event.target)) {
                dropdown.style.display = 'none';
            }
        });

        function previewTemplate(templateId) {
            const modal = document.getElementById('previewModal');
            const frame = document.getElementById('previewFrame');
            
            // تحميل معاينة القالب
            frame.src = `template_preview.php?id=${templateId}`;
            modal.style.display = 'block';
        }

        function closePreview() {
            const modal = document.getElementById('previewModal');
            modal.style.display = 'none';
        }

        // إغلاق النافذة عند النقر خارجها
        window.onclick = function(event) {
            const modal = document.getElementById('previewModal');
            if (event.target == modal) {
                closePreview();
            }
        }

        // تحميل معاينات مصغرة للقوالب
        document.addEventListener('DOMContentLoaded', function() {
            const templates = <?php echo json_encode($templates); ?>;
            
            templates.forEach(template => {
                loadTemplatePreview(template.id, template.css_variables);
            });
        });

        function loadTemplatePreview(templateId, cssVariables) {
            const previewElement = document.getElementById(`preview-${templateId}`);
            if (!previewElement) return;
            
            const variables = JSON.parse(cssVariables);
            
            // إنشاء معاينة مصغرة للقالب
            previewElement.innerHTML = `
                <div style="
                    width: 300px; 
                    height: 200px; 
                    background: ${variables.background}; 
                    color: ${variables.text};
                    padding: 20px;
                    font-family: Cairo, sans-serif;
                    border: 1px solid #eee;
                ">
                    <div style="
                        background: ${variables.primary}; 
                        color: white; 
                        padding: 10px; 
                        margin-bottom: 10px;
                        border-radius: 4px;
                    ">
                        <h3 style="margin: 0; font-size: 14px;">فاتورة رقم 001</h3>
                    </div>
                    <div style="background: ${variables.accent}; padding: 8px; margin-bottom: 8px; border-radius: 4px;">
                        <div style="font-size: 10px; margin-bottom: 4px;">العميل: شركة المثال</div>
                        <div style="font-size: 10px;">التاريخ: ${new Date().toLocaleDateString('ar-SA')}</div>
                    </div>
                    <div style="
                        background: ${variables.secondary}; 
                        color: white; 
                        padding: 6px; 
                        text-align: center; 
                        border-radius: 4px;
                        font-size: 10px;
                    ">
                        المجموع: 1,000 ر.س
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html>
