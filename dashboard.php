<?php
require_once 'includes/middleware.php';

// التحقق من تسجيل الدخول
requireAuth();

$user = getCurrentUser();

// الحصول على إحصائيات المستخدم
function getUserStats($userId) {
    global $db;
    
    $stats = [
        'total_invoices' => 0,
        'total_clients' => 0,
        'total_amount' => 0,
        'amount_due' => 0,
        'paid_invoices' => 0,
        'draft_invoices' => 0,
        'overdue_invoices' => 0
    ];
    
    try {
        // إحصائيات الفواتير
        $query = "SELECT 
                    COUNT(*) as total_invoices,
                    SUM(total_amount) as total_amount,
                    SUM(amount_due) as amount_due,
                    SUM(CASE WHEN status = 'paid' THEN 1 ELSE 0 END) as paid_invoices,
                    SUM(CASE WHEN status = 'draft' THEN 1 ELSE 0 END) as draft_invoices,
                    SUM(CASE WHEN status = 'overdue' THEN 1 ELSE 0 END) as overdue_invoices
                  FROM invoices WHERE user_id = ?";
        
        $stmt = $db->prepare($query);
        $stmt->execute([$userId]);
        $invoiceStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($invoiceStats) {
            $stats = array_merge($stats, $invoiceStats);
        }
        
        // إحصائيات العملاء
        $query = "SELECT COUNT(*) as total_clients FROM clients WHERE user_id = ?";
        $stmt = $db->prepare($query);
        $stmt->execute([$userId]);
        $clientStats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($clientStats) {
            $stats['total_clients'] = $clientStats['total_clients'];
        }
        
    } catch (Exception $e) {
        Config::logError("Error getting user stats: " . $e->getMessage());
    }
    
    return $stats;
}

// الحصول على آخر الفواتير
function getRecentInvoices($userId, $limit = 5) {
    global $db;
    
    try {
        $query = "SELECT i.*, c.name as client_name 
                  FROM invoices i 
                  LEFT JOIN clients c ON i.client_id = c.id 
                  WHERE i.user_id = ? 
                  ORDER BY i.created_at DESC 
                  LIMIT ?";
        
        $stmt = $db->prepare($query);
        $stmt->execute([$userId, $limit]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        Config::logError("Error getting recent invoices: " . $e->getMessage());
        return [];
    }
}

$stats = getUserStats($user['id']);
$recentInvoices = getRecentInvoices($user['id']);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إنشاء الفواتير</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .dashboard-header {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .welcome-section h1 {
            font-size: 28px;
            color: #2c3e50;
            margin: 0 0 5px 0;
        }

        .welcome-section p {
            color: #6c757d;
            margin: 0;
        }

        .user-actions {
            display: flex;
            gap: 10px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 24px;
            color: white;
        }

        .stat-icon.invoices { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .stat-icon.clients { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .stat-icon.revenue { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .stat-icon.pending { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

        .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 14px;
        }

        .dashboard-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }

        .recent-invoices {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .section-header {
            padding: 20px 25px;
            border-bottom: 1px solid #e1e8ed;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
        }

        .invoices-table {
            width: 100%;
            border-collapse: collapse;
        }

        .invoices-table th {
            background-color: #f8f9fa;
            padding: 15px;
            text-align: right;
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
        }

        .invoices-table td {
            padding: 15px;
            border-bottom: 1px solid #e1e8ed;
            font-size: 14px;
        }

        .invoices-table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-draft { background-color: #ffc107; color: #856404; }
        .status-sent { background-color: #17a2b8; color: white; }
        .status-paid { background-color: #28a745; color: white; }
        .status-overdue { background-color: #dc3545; color: white; }

        .quick-actions {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 25px;
        }

        .action-btn {
            display: block;
            width: 100%;
            padding: 15px;
            margin-bottom: 15px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            text-align: center;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .action-btn:last-child {
            margin-bottom: 0;
        }

        .action-btn.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .action-btn.secondary {
            background: #f8f9fa;
            color: #2c3e50;
            border: 1px solid #e1e8ed;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .navbar {
            background: white;
            padding: 15px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .navbar-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .navbar-brand {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            text-decoration: none;
        }

        .navbar-nav {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .nav-link {
            color: #6c757d;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            background-color: #f8f9fa;
            color: #2c3e50;
        }

        .user-menu {
            position: relative;
            display: inline-block;
        }

        .user-toggle {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            background: #f8f9fa;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            color: #2c3e50;
        }

        @media (max-width: 768px) {
            .dashboard-content {
                grid-template-columns: 1fr;
            }
            
            .dashboard-header {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar">
        <div class="navbar-content">
            <a href="dashboard.php" class="navbar-brand">
                <i class="fas fa-file-invoice"></i> نظام الفواتير
            </a>
            
            <div class="navbar-nav">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-home"></i> الرئيسية
                </a>
                <a href="index.php" class="nav-link">
                    <i class="fas fa-plus"></i> فاتورة جديدة
                </a>
                <a href="invoices.php" class="nav-link">
                    <i class="fas fa-file-invoice-dollar"></i> الفواتير
                </a>
                <a href="templates.php" class="nav-link">
                    <i class="fas fa-palette"></i> القوالب
                </a>

                <div class="user-menu">
                    <button class="user-toggle" onclick="toggleUserMenu()">
                        <i class="fas fa-user"></i>
                        <?php echo htmlspecialchars($user['full_name']); ?>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div id="userDropdown" class="dropdown-menu" style="display: none;">
                        <a href="profile.php" class="dropdown-item">
                            <i class="fas fa-user-edit"></i> الملف الشخصي
                        </a>
                        <a href="logout.php" class="dropdown-item">
                            <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="dashboard-container">
        <!-- رأس لوحة التحكم -->
        <div class="dashboard-header">
            <div class="welcome-section">
                <h1>مرحباً، <?php echo htmlspecialchars($user['full_name']); ?></h1>
                <p>
                    <?php if ($user['company_name']): ?>
                        <?php echo htmlspecialchars($user['company_name']); ?> • 
                    <?php endif; ?>
                    آخر تسجيل دخول: <?php echo $user['last_login'] ? date('d/m/Y H:i', strtotime($user['last_login'])) : 'لم يسجل من قبل'; ?>
                </p>
            </div>
            
            <div class="user-actions">
                <a href="index.php" class="btn btn-primary">
                    <i class="fas fa-plus"></i> فاتورة جديدة
                </a>
            </div>
        </div>

        <!-- بطاقات الإحصائيات -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon invoices">
                    <i class="fas fa-file-invoice"></i>
                </div>
                <div class="stat-number"><?php echo number_format($stats['total_invoices']); ?></div>
                <div class="stat-label">إجمالي الفواتير</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon clients">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-number"><?php echo number_format($stats['total_clients']); ?></div>
                <div class="stat-label">العملاء</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon revenue">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-number"><?php echo number_format($stats['total_amount'], 2); ?></div>
                <div class="stat-label">إجمالي المبيعات (ر.س)</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon pending">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-number"><?php echo number_format($stats['amount_due'], 2); ?></div>
                <div class="stat-label">المبالغ المستحقة (ر.س)</div>
            </div>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="dashboard-content">
            <!-- آخر الفواتير -->
            <div class="recent-invoices">
                <div class="section-header">
                    <h2 class="section-title">آخر الفواتير</h2>
                    <a href="invoices.php" class="btn btn-secondary btn-sm">عرض الكل</a>
                </div>
                
                <?php if (empty($recentInvoices)): ?>
                    <div style="padding: 40px; text-align: center; color: #6c757d;">
                        <i class="fas fa-file-invoice" style="font-size: 48px; margin-bottom: 15px; opacity: 0.3;"></i>
                        <p>لا توجد فواتير بعد</p>
                        <a href="index.php" class="btn btn-primary">إنشاء أول فاتورة</a>
                    </div>
                <?php else: ?>
                    <table class="invoices-table">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recentInvoices as $invoice): ?>
                                <tr>
                                    <td>
                                        <a href="view_invoice.php?id=<?php echo $invoice['id']; ?>" 
                                           style="color: #667eea; text-decoration: none; font-weight: 500;">
                                            <?php echo htmlspecialchars($invoice['invoice_number']); ?>
                                        </a>
                                    </td>
                                    <td><?php echo htmlspecialchars($invoice['client_name'] ?: 'غير محدد'); ?></td>
                                    <td><?php echo number_format($invoice['total_amount'], 2); ?> ر.س</td>
                                    <td>
                                        <span class="status-badge status-<?php echo $invoice['status']; ?>">
                                            <?php echo Config::getStatusText($invoice['status']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo date('d/m/Y', strtotime($invoice['created_at'])); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>

            <!-- الإجراءات السريعة -->
            <div class="quick-actions">
                <h3 class="section-title" style="margin-bottom: 20px;">الإجراءات السريعة</h3>
                
                <a href="index.php" class="action-btn primary">
                    <i class="fas fa-plus"></i> إنشاء فاتورة جديدة
                </a>
                
                <a href="invoices.php" class="action-btn secondary">
                    <i class="fas fa-list"></i> عرض جميع الفواتير
                </a>
                
                <a href="templates.php" class="action-btn secondary">
                    <i class="fas fa-palette"></i> قوالب الفواتير
                </a>

                <a href="profile.php" class="action-btn secondary">
                    <i class="fas fa-user-cog"></i> إعدادات الحساب
                </a>
            </div>
        </div>
    </div>

    <script>
        function toggleUserMenu() {
            const dropdown = document.getElementById('userDropdown');
            dropdown.style.display = dropdown.style.display === 'none' ? 'block' : 'none';
        }

        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', function(event) {
            const userMenu = document.querySelector('.user-menu');
            const dropdown = document.getElementById('userDropdown');
            
            if (!userMenu.contains(event.target)) {
                dropdown.style.display = 'none';
            }
        });
    </script>
</body>
</html>
