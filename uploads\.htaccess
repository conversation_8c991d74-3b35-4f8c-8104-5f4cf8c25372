# منع تنفيذ ملفات PHP في مجلد الرفع
<Files "*.php">
    Order Deny,Allow
    Deny from all
</Files>

# منع الوصول إلى ملفات النظام
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# السماح بأنواع ملفات محددة فقط
<FilesMatch "\.(jpg|jpeg|png|gif|webp|pdf|doc|docx|xls|xlsx|txt)$">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# منع الوصول إلى جميع الملفات الأخرى
<FilesMatch "^(?!.*\.(jpg|jpeg|png|gif|webp|pdf|doc|docx|xls|xlsx|txt)$).*$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# إعدادات الأمان الإضافية
Options -Indexes
Options -ExecCGI

# منع الوصول المباشر إلى الملفات الحساسة
<Files "*.log">
    Order Deny,Allow
    Deny from all
</Files>

# تعيين headers الأمان
<IfModule mod_headers.c>
    Header set X-Content-Type-Options nosniff
    Header set X-Frame-Options DENY
    Header set X-XSS-Protection "1; mode=block"
</IfModule>

# تحديد أنواع MIME للملفات المسموحة
<IfModule mod_mime.c>
    AddType image/jpeg .jpg .jpeg
    AddType image/png .png
    AddType image/gif .gif
    AddType image/webp .webp
    AddType application/pdf .pdf
    AddType application/msword .doc
    AddType application/vnd.openxmlformats-officedocument.wordprocessingml.document .docx
    AddType application/vnd.ms-excel .xls
    AddType application/vnd.openxmlformats-officedocument.spreadsheetml.sheet .xlsx
    AddType text/plain .txt
</IfModule>
