// نظام إدارة قوالب الفواتير

class TemplateManager {
    constructor() {
        this.currentTemplate = null;
        this.templates = {};
        this.init();
    }

    // تهيئة النظام
    init() {
        this.loadUserTemplate();
        this.bindEvents();
    }

    // تحميل القالب المفضل للمستخدم
    loadUserTemplate() {
        // محاولة تحميل القالب من الجلسة أولاً
        const sessionTemplate = sessionStorage.getItem('selected_template');
        if (sessionTemplate) {
            this.applyTemplate(JSON.parse(sessionTemplate));
            return;
        }

        // تحميل القالب الافتراضي من الخادم
        fetch('api/get_user_template.php')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.template) {
                    this.applyTemplate(data.template);
                } else {
                    // تطبيق القالب الافتراضي
                    this.applyDefaultTemplate();
                }
            })
            .catch(error => {
                console.error('Error loading user template:', error);
                this.applyDefaultTemplate();
            });
    }

    // تطبيق القالب على الصفحة
    applyTemplate(template) {
        this.currentTemplate = template;
        
        // حفظ القالب في الجلسة
        sessionStorage.setItem('selected_template', JSON.stringify(template));
        
        // تطبيق متغيرات CSS
        this.applyCSSVariables(template);
        
        // تطبيق فئات CSS
        this.applyCSSClasses(template);
        
        // تحديث عناصر الواجهة
        this.updateUIElements(template);
    }

    // تطبيق متغيرات CSS
    applyCSSVariables(template) {
        const root = document.documentElement;
        const variables = JSON.parse(template.css_variables);
        
        root.style.setProperty('--template-primary', variables.primary);
        root.style.setProperty('--template-secondary', variables.secondary);
        root.style.setProperty('--template-accent', variables.accent);
        root.style.setProperty('--template-text', variables.text);
        root.style.setProperty('--template-background', variables.background);
    }

    // تطبيق فئات CSS
    applyCSSClasses(template) {
        const body = document.body;
        
        // إزالة الفئات السابقة
        body.classList.remove(
            'template-applied', 'template-classic', 'template-modern', 
            'template-creative', 'template-minimal', 'template-professional'
        );
        
        // إزالة فئات الألوان السابقة
        const colorClasses = body.classList.toString().match(/color-\w+/g);
        if (colorClasses) {
            colorClasses.forEach(cls => body.classList.remove(cls));
        }
        
        // إضافة الفئات الجديدة
        body.classList.add('template-applied');
        body.classList.add(`template-${template.category}`);
        body.classList.add(`color-${template.color_scheme}`);
    }

    // تحديث عناصر الواجهة
    updateUIElements(template) {
        // تحديث عنوان الصفحة إذا كان موجوداً
        const templateIndicator = document.getElementById('current-template');
        if (templateIndicator) {
            templateIndicator.textContent = template.name;
        }

        // تحديث الأزرار والعناصر التفاعلية
        this.updateButtons();
        
        // تحديث جدول العناصر
        this.updateItemsTable();
        
        // تحديث ملخص المبالغ
        this.updateSummaryTable();
    }

    // تحديث الأزرار
    updateButtons() {
        const buttons = document.querySelectorAll('.btn-primary, .btn-secondary');
        buttons.forEach(button => {
            // إضافة تأثيرات hover محسنة
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });
            
            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    }

    // تحديث جدول العناصر
    updateItemsTable() {
        const table = document.querySelector('.items-table');
        if (table) {
            // إضافة تأثيرات hover للصفوف
            const rows = table.querySelectorAll('tbody tr');
            rows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = 'rgba(0,0,0,0.02)';
                });
                
                row.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '';
                });
            });
        }
    }

    // تحديث ملخص المبالغ
    updateSummaryTable() {
        const summaryTable = document.querySelector('.summary-table');
        if (summaryTable) {
            // تحديث ألوان الصف الإجمالي
            const totalRow = summaryTable.querySelector('.total-row');
            if (totalRow) {
                const variables = JSON.parse(this.currentTemplate.css_variables);
                totalRow.style.backgroundColor = variables.primary;
                totalRow.style.color = 'white';
            }
        }
    }

    // تطبيق القالب الافتراضي
    applyDefaultTemplate() {
        const defaultTemplate = {
            id: 1,
            name: 'الكلاسيكي الأزرق',
            category: 'classic',
            color_scheme: 'blue',
            css_variables: JSON.stringify({
                primary: '#2c3e50',
                secondary: '#3498db',
                accent: '#e3f2fd',
                text: '#2c3e50',
                background: '#ffffff'
            })
        };
        
        this.applyTemplate(defaultTemplate);
    }

    // تغيير القالب
    changeTemplate(templateId) {
        fetch(`api/get_template.php?id=${templateId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.applyTemplate(data.template);
                    
                    // حفظ القالب كافتراضي للمستخدم
                    this.saveUserTemplate(templateId);
                } else {
                    console.error('Error loading template:', data.message);
                }
            })
            .catch(error => {
                console.error('Error changing template:', error);
            });
    }

    // حفظ القالب المفضل للمستخدم
    saveUserTemplate(templateId) {
        fetch('api/save_user_template.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                template_id: templateId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Template saved successfully');
            } else {
                console.error('Error saving template:', data.message);
            }
        })
        .catch(error => {
            console.error('Error saving template:', error);
        });
    }

    // معاينة القالب مؤقتاً
    previewTemplate(template) {
        // حفظ القالب الحالي
        const currentTemplate = this.currentTemplate;
        
        // تطبيق القالب الجديد مؤقتاً
        this.applyTemplate(template);
        
        // إرجاع دالة لاستعادة القالب الأصلي
        return () => {
            if (currentTemplate) {
                this.applyTemplate(currentTemplate);
            } else {
                this.applyDefaultTemplate();
            }
        };
    }

    // ربط الأحداث
    bindEvents() {
        // حدث تغيير القالب من صفحة القوالب
        window.addEventListener('template-changed', (event) => {
            this.changeTemplate(event.detail.templateId);
        });

        // حدث معاينة القالب
        window.addEventListener('template-preview', (event) => {
            const restoreFunction = this.previewTemplate(event.detail.template);
            
            // حفظ دالة الاستعادة للاستخدام لاحقاً
            window.templateRestoreFunction = restoreFunction;
        });

        // حدث إنهاء المعاينة
        window.addEventListener('template-preview-end', () => {
            if (window.templateRestoreFunction) {
                window.templateRestoreFunction();
                delete window.templateRestoreFunction;
            }
        });
    }

    // الحصول على القالب الحالي
    getCurrentTemplate() {
        return this.currentTemplate;
    }

    // الحصول على متغيرات CSS الحالية
    getCurrentCSSVariables() {
        if (!this.currentTemplate) return null;
        return JSON.parse(this.currentTemplate.css_variables);
    }
}

// دوال مساعدة عامة

// تطبيق القالب على عنصر محدد
function applyTemplateToElement(element, template) {
    const variables = JSON.parse(template.css_variables);
    
    element.style.setProperty('--template-primary', variables.primary);
    element.style.setProperty('--template-secondary', variables.secondary);
    element.style.setProperty('--template-accent', variables.accent);
    element.style.setProperty('--template-text', variables.text);
    element.style.setProperty('--template-background', variables.background);
    
    element.classList.add('template-applied');
    element.classList.add(`template-${template.category}`);
    element.classList.add(`color-${template.color_scheme}`);
}

// إزالة القالب من عنصر محدد
function removeTemplateFromElement(element) {
    element.classList.remove(
        'template-applied', 'template-classic', 'template-modern', 
        'template-creative', 'template-minimal', 'template-professional'
    );
    
    // إزالة فئات الألوان
    const colorClasses = element.classList.toString().match(/color-\w+/g);
    if (colorClasses) {
        colorClasses.forEach(cls => element.classList.remove(cls));
    }
    
    // إزالة متغيرات CSS
    element.style.removeProperty('--template-primary');
    element.style.removeProperty('--template-secondary');
    element.style.removeProperty('--template-accent');
    element.style.removeProperty('--template-text');
    element.style.removeProperty('--template-background');
}

// تحديث ألوان عنصر بناءً على القالب
function updateElementColors(element, template) {
    const variables = JSON.parse(template.css_variables);
    
    if (element.classList.contains('btn-primary')) {
        element.style.backgroundColor = variables.primary;
        element.style.borderColor = variables.primary;
    } else if (element.classList.contains('btn-secondary')) {
        element.style.borderColor = variables.secondary;
        element.style.color = variables.secondary;
    }
}

// إنشاء مثيل من مدير القوالب
let templateManager;

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحميل ملف CSS الخاص بالقوالب
    const templateCSS = document.createElement('link');
    templateCSS.rel = 'stylesheet';
    templateCSS.href = 'css/templates.css';
    document.head.appendChild(templateCSS);
    
    // إنشاء مدير القوالب
    templateManager = new TemplateManager();
    
    // جعل مدير القوالب متاحاً عالمياً
    window.templateManager = templateManager;
});

// دوال للاستخدام في صفحات أخرى

// تغيير القالب
function changeTemplate(templateId) {
    if (window.templateManager) {
        window.templateManager.changeTemplate(templateId);
    }
}

// معاينة القالب
function previewTemplate(template) {
    if (window.templateManager) {
        return window.templateManager.previewTemplate(template);
    }
}

// الحصول على القالب الحالي
function getCurrentTemplate() {
    if (window.templateManager) {
        return window.templateManager.getCurrentTemplate();
    }
    return null;
}

// تصدير الدوال للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        TemplateManager,
        applyTemplateToElement,
        removeTemplateFromElement,
        updateElementColors,
        changeTemplate,
        previewTemplate,
        getCurrentTemplate
    };
}
