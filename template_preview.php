<?php
require_once 'includes/middleware.php';

// التحقق من تسجيل الدخول
requireAuth();

$user = getCurrentUser();
$templateId = $_GET['id'] ?? 1;

// الحصول على بيانات القالب
function getTemplate($id) {
    global $db;
    
    $query = "SELECT * FROM invoice_templates WHERE id = ? AND is_active = 1";
    $stmt = $db->prepare($query);
    $stmt->execute([$id]);
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

$template = getTemplate($templateId);

if (!$template) {
    die('القالب غير موجود');
}

$cssVariables = json_decode($template['css_variables'], true);

// بيانات تجريبية للمعاينة
$sampleData = [
    'invoice_number' => '000123',
    'issue_date' => date('Y-m-d'),
    'due_date' => date('Y-m-d', strtotime('+30 days')),
    'client_name' => 'شركة المثال للتجارة',
    'client_email' => '<EMAIL>',
    'client_phone' => '+966501234567',
    'client_address' => 'الرياض، المملكة العربية السعودية',
    'company_name' => $user['company_name'] ?: $user['full_name'],
    'company_phone' => $user['phone'] ?: '+966504280365',
    'items' => [
        [
            'name' => 'خدمة استشارية',
            'description' => 'استشارة تقنية متخصصة',
            'quantity' => 2,
            'rate' => 500.00,
            'amount' => 1000.00
        ],
        [
            'name' => 'تطوير موقع إلكتروني',
            'description' => 'تصميم وتطوير موقع متجاوب',
            'quantity' => 1,
            'rate' => 2500.00,
            'amount' => 2500.00
        ],
        [
            'name' => 'خدمة الاستضافة',
            'description' => 'استضافة سنوية مع دعم فني',
            'quantity' => 1,
            'rate' => 300.00,
            'amount' => 300.00
        ]
    ],
    'subtotal' => 3800.00,
    'discount_percentage' => 5,
    'discount_amount' => 190.00,
    'tax_rate' => 15,
    'tax_amount' => 541.50,
    'total_amount' => 4151.50,
    'notes' => 'شكراً لتعاملكم معنا. نتطلع للعمل معكم مرة أخرى.',
    'terms' => 'يرجى السداد خلال 30 يوم من تاريخ الفاتورة. رسوم تأخير 2% شهرياً بعد تاريخ الاستحقاق.'
];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معاينة القالب - <?php echo htmlspecialchars($template['name']); ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: <?php echo $cssVariables['primary']; ?>;
            --secondary-color: <?php echo $cssVariables['secondary']; ?>;
            --accent-color: <?php echo $cssVariables['accent']; ?>;
            --text-color: <?php echo $cssVariables['text']; ?>;
            --background-color: <?php echo $cssVariables['background']; ?>;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
            padding: 20px;
        }

        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .invoice-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 40px;
            position: relative;
        }

        .invoice-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="3" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 1;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .company-info h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .company-details {
            opacity: 0.9;
            font-size: 14px;
        }

        .invoice-meta {
            text-align: left;
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 8px;
        }

        .invoice-number {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .invoice-body {
            padding: 40px;
        }

        .billing-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .billing-info {
            background: var(--accent-color);
            padding: 25px;
            border-radius: 8px;
            border-right: 4px solid var(--primary-color);
        }

        .billing-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .items-table th {
            background: var(--primary-color);
            color: white;
            padding: 15px;
            text-align: right;
            font-weight: 600;
        }

        .items-table td {
            padding: 15px;
            border-bottom: 1px solid #eee;
        }

        .items-table tbody tr:nth-child(even) {
            background: var(--accent-color);
        }

        .items-table tbody tr:hover {
            background: rgba(0,0,0,0.02);
        }

        .item-description {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        .summary-section {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 40px;
        }

        .summary-table {
            width: 350px;
            border-collapse: collapse;
        }

        .summary-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }

        .summary-table .label {
            font-weight: 500;
            color: var(--text-color);
        }

        .summary-table .amount {
            text-align: left;
            font-weight: 600;
        }

        .summary-table .total-row {
            background: var(--primary-color);
            color: white;
            font-size: 18px;
            font-weight: 700;
        }

        .notes-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 40px;
            padding-top: 30px;
            border-top: 2px solid var(--accent-color);
        }

        .notes-box {
            background: var(--accent-color);
            padding: 20px;
            border-radius: 8px;
            border-right: 4px solid var(--secondary-color);
        }

        .notes-title {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .footer {
            background: var(--accent-color);
            padding: 20px 40px;
            text-align: center;
            color: var(--text-color);
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 20px;
            }
            
            .billing-section {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .notes-section {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .invoice-body {
                padding: 20px;
            }
        }

        /* تخصيصات إضافية حسب فئة القالب */
        <?php if ($template['category'] === 'creative'): ?>
        .invoice-header {
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color), var(--primary-color));
            background-size: 200% 200%;
            animation: gradientShift 3s ease infinite;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        <?php endif; ?>

        <?php if ($template['category'] === 'minimal'): ?>
        .invoice-header {
            background: var(--primary-color);
            padding: 30px 40px;
        }
        
        .billing-info {
            border: 1px solid var(--primary-color);
            border-right: 4px solid var(--primary-color);
        }
        <?php endif; ?>

        <?php if ($template['category'] === 'professional'): ?>
        .invoice-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
        }
        <?php endif; ?>
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- رأس الفاتورة -->
        <div class="invoice-header">
            <div class="header-content">
                <div class="company-info">
                    <h1><?php echo htmlspecialchars($sampleData['company_name']); ?></h1>
                    <div class="company-details">
                        <div><i class="fas fa-phone"></i> <?php echo htmlspecialchars($sampleData['company_phone']); ?></div>
                        <div><i class="fas fa-map-marker-alt"></i> المملكة العربية السعودية</div>
                    </div>
                </div>
                
                <div class="invoice-meta">
                    <div class="invoice-number">فاتورة رقم: <?php echo $sampleData['invoice_number']; ?></div>
                    <div>تاريخ الإصدار: <?php echo date('d/m/Y', strtotime($sampleData['issue_date'])); ?></div>
                    <div>تاريخ الاستحقاق: <?php echo date('d/m/Y', strtotime($sampleData['due_date'])); ?></div>
                </div>
            </div>
        </div>

        <!-- محتوى الفاتورة -->
        <div class="invoice-body">
            <!-- معلومات الفوترة -->
            <div class="billing-section">
                <div class="billing-info">
                    <div class="billing-title">
                        <i class="fas fa-user"></i> فاتورة إلى:
                    </div>
                    <div><strong><?php echo htmlspecialchars($sampleData['client_name']); ?></strong></div>
                    <div><?php echo htmlspecialchars($sampleData['client_email']); ?></div>
                    <div><?php echo htmlspecialchars($sampleData['client_phone']); ?></div>
                    <div><?php echo htmlspecialchars($sampleData['client_address']); ?></div>
                </div>
                
                <div class="billing-info">
                    <div class="billing-title">
                        <i class="fas fa-building"></i> من:
                    </div>
                    <div><strong><?php echo htmlspecialchars($sampleData['company_name']); ?></strong></div>
                    <div><?php echo htmlspecialchars($sampleData['company_phone']); ?></div>
                    <div>المملكة العربية السعودية</div>
                </div>
            </div>

            <!-- جدول العناصر -->
            <table class="items-table">
                <thead>
                    <tr>
                        <th>الوصف</th>
                        <th>السعر</th>
                        <th>الكمية</th>
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($sampleData['items'] as $item): ?>
                        <tr>
                            <td>
                                <strong><?php echo htmlspecialchars($item['name']); ?></strong>
                                <div class="item-description"><?php echo htmlspecialchars($item['description']); ?></div>
                            </td>
                            <td><?php echo number_format($item['rate'], 2); ?> ر.س</td>
                            <td><?php echo $item['quantity']; ?></td>
                            <td><?php echo number_format($item['amount'], 2); ?> ر.س</td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>

            <!-- ملخص المبالغ -->
            <div class="summary-section">
                <table class="summary-table">
                    <tr>
                        <td class="label">المجموع الفرعي:</td>
                        <td class="amount"><?php echo number_format($sampleData['subtotal'], 2); ?> ر.س</td>
                    </tr>
                    <tr>
                        <td class="label">الخصم (<?php echo $sampleData['discount_percentage']; ?>%):</td>
                        <td class="amount">-<?php echo number_format($sampleData['discount_amount'], 2); ?> ر.س</td>
                    </tr>
                    <tr>
                        <td class="label">الضريبة (<?php echo $sampleData['tax_rate']; ?>%):</td>
                        <td class="amount"><?php echo number_format($sampleData['tax_amount'], 2); ?> ر.س</td>
                    </tr>
                    <tr class="total-row">
                        <td class="label">الإجمالي:</td>
                        <td class="amount"><?php echo number_format($sampleData['total_amount'], 2); ?> ر.س</td>
                    </tr>
                </table>
            </div>

            <!-- الملاحظات والشروط -->
            <div class="notes-section">
                <div class="notes-box">
                    <div class="notes-title">
                        <i class="fas fa-sticky-note"></i> ملاحظات:
                    </div>
                    <p><?php echo htmlspecialchars($sampleData['notes']); ?></p>
                </div>
                
                <div class="notes-box">
                    <div class="notes-title">
                        <i class="fas fa-file-contract"></i> الشروط والأحكام:
                    </div>
                    <p><?php echo htmlspecialchars($sampleData['terms']); ?></p>
                </div>
            </div>
        </div>

        <!-- تذييل الفاتورة -->
        <div class="footer">
            <p>شكراً لتعاملكم معنا • <?php echo htmlspecialchars($template['name']); ?></p>
        </div>
    </div>
</body>
</html>
