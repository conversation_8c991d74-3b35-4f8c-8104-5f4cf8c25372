<?php
require_once 'includes/middleware.php';

// التحقق من تسجيل الدخول
requireAuth();

$user = getCurrentUser();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة جديدة - نظام إنشاء الفواتير</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        .navbar {
            background: white;
            padding: 15px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .navbar-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .navbar-brand {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            text-decoration: none;
        }

        .navbar-nav {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .nav-link {
            color: #6c757d;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .nav-link:hover, .nav-link.active {
            background-color: #f8f9fa;
            color: #2c3e50;
        }

        .user-menu {
            position: relative;
            display: inline-block;
        }

        .user-toggle {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            background: #f8f9fa;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            color: #2c3e50;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 6px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            min-width: 180px;
            z-index: 1000;
        }

        .dropdown-item {
            display: block;
            padding: 10px 15px;
            color: #2c3e50;
            text-decoration: none;
            transition: background-color 0.3s ease;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar">
        <div class="navbar-content">
            <a href="dashboard.php" class="navbar-brand">
                <i class="fas fa-file-invoice"></i> نظام الفواتير
            </a>

            <div class="navbar-nav">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-home"></i> الرئيسية
                </a>
                <a href="index.php" class="nav-link active">
                    <i class="fas fa-plus"></i> فاتورة جديدة
                </a>
                <a href="invoices.php" class="nav-link">
                    <i class="fas fa-file-invoice-dollar"></i> الفواتير
                </a>

                <div class="user-menu">
                    <button class="user-toggle" onclick="toggleUserMenu()">
                        <i class="fas fa-user"></i>
                        <?php echo htmlspecialchars($user['full_name']); ?>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div id="userDropdown" class="dropdown-menu" style="display: none;">
                        <a href="profile.php" class="dropdown-item">
                            <i class="fas fa-user-edit"></i> الملف الشخصي
                        </a>
                        <a href="logout.php" class="dropdown-item">
                            <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="container">
        <header class="invoice-header">
            <h1>فاتورة جديدة</h1>
            <div class="header-buttons">
                <button class="btn btn-secondary" onclick="cancelInvoice()">إلغاء</button>
                <button class="btn btn-primary" onclick="saveInvoice()">حفظ</button>
                <button class="btn btn-success" onclick="sendInvoice()">إرسال إلى...</button>
            </div>
        </header>

        <div class="invoice-content">
            <div class="main-section">
                <!-- Header Section with Logo and Company Info -->
                <div class="invoice-header-section">
                    <!-- Logo Upload Section -->
                    <div class="logo-section">
                        <div class="logo-upload" id="logoUpload">
                            <i class="fas fa-image"></i>
                            <p>اسحب الشعار هنا،<br>أو <span class="select-file">اختر ملف</span></p>
                            <input type="file" id="logoFile" accept="image/*" style="display: none;">
                        </div>
                    </div>

                    <!-- Company Info -->
                    <div class="company-info">
                        <div class="company-details">
                            <p class="invoice-number">رقم الفاتورة: <span id="invoiceNumber">000001</span></p>
                            <p class="company-name"><?php echo htmlspecialchars($user['company_name'] ?: $user['full_name']); ?></p>
                            <p class="company-id"><?php echo htmlspecialchars($user['phone'] ?: '0504280365'); ?></p>
                            <p class="country">المملكة العربية السعودية</p>
                            <a href="profile.php" class="edit-business-info">تحرير معلومات الشركة</a>
                        </div>
                    </div>
                </div>

                <!-- Invoice Details -->
                <div class="invoice-details">
                    <div class="detail-row">
                        <div class="detail-group">
                            <label>مرسل إلى</label>
                            <select id="clientSelect" class="form-control">
                                <option value="">اختر عميل</option>
                            </select>
                            <a href="#" class="create-client" onclick="createClient()">+ إنشاء عميل</a>
                        </div>

                        <div class="detail-group">
                            <label>تاريخ الإصدار</label>
                            <input type="date" id="issueDate" class="form-control" value="<?php echo date('Y-m-d'); ?>">
                        </div>

                        <div class="detail-group">
                            <label>رقم الفاتورة</label>
                            <input type="text" id="invoiceNum" class="form-control" value="000001">
                        </div>

                        <div class="detail-group">
                            <label>المبلغ المستحق (ر.س)</label>
                            <input type="text" id="amountDue" class="form-control amount-display" value="0.00 ر.س" readonly>
                        </div>
                    </div>

                    <div class="detail-row">
                        <div class="detail-group">
                            <label>تاريخ الاستحقاق</label>
                            <input type="date" id="dueDate" class="form-control">
                        </div>

                        <div class="detail-group">
                            <label>المرجع</label>
                            <input type="text" id="reference" class="form-control" placeholder="أدخل القيمة (مثل: PO#)">
                        </div>
                    </div>
                </div>

                <!-- Invoice Items Table -->
                <div class="invoice-table">
                    <table id="itemsTable">
                        <thead>
                            <tr>
                                <th>الوصف</th>
                                <th>السعر</th>
                                <th>الكمية</th>
                                <th>إجمالي السطر</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody id="itemsTableBody">
                            <tr class="item-row">
                                <td>
                                    <input type="text" class="form-control item-name" placeholder="أدخل اسم العنصر">
                                    <input type="text" class="form-control item-description" placeholder="أدخل وصف العنصر">
                                </td>
                                <td>
                                    <input type="number" class="form-control item-rate" placeholder="0.00" step="0.01" onchange="calculateLineTotal(this)">
                                    <small class="tax-note">شامل الضريبة</small>
                                </td>
                                <td>
                                    <input type="number" class="form-control item-qty" value="1" min="1" onchange="calculateLineTotal(this)">
                                </td>
                                <td>
                                    <span class="line-total">0.00 ر.س</span>
                                </td>
                                <td>
                                    <button class="btn-remove" onclick="removeItem(this)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <button class="add-line-btn" onclick="addNewLine()">
                        <i class="fas fa-plus"></i> إضافة سطر
                    </button>
                </div>

                <!-- Invoice Summary -->
                <div class="invoice-summary">
                    <div class="summary-row">
                        <span>المجموع الفرعي</span>
                        <span id="subtotal">0.00</span>
                    </div>
                    <div class="summary-row">
                        <span>إضافة خصم</span>
                        <input type="number" id="discount" class="discount-input" placeholder="0.00" step="0.01" onchange="calculateTotal()">
                    </div>
                    <div class="summary-row">
                        <span>الضريبة</span>
                        <select id="taxRate" class="tax-select" onchange="calculateTotal()">
                            <option value="0">0%</option>
                            <option value="5">5%</option>
                            <option value="15" selected>15%</option>
                        </select>
                        <span id="taxAmount">0.00</span>
                    </div>
                    <div class="summary-row total-row">
                        <span>الإجمالي</span>
                        <span id="total">0.00</span>
                    </div>
                    <div class="summary-row">
                        <span>المبلغ المدفوع</span>
                        <span id="amountPaid">0.00</span>
                    </div>
                    <div class="summary-row amount-due-row">
                        <span>المبلغ المستحق (ر.س)</span>
                        <span id="finalAmountDue">0.00 ر.س</span>
                        <a href="#" class="request-deposit">طلب إيداع</a>
                    </div>
                </div>

                <!-- Notes Section -->
                <div class="notes-section">
                    <label>ملاحظات</label>
                    <textarea id="notes" class="form-control" placeholder="أدخل الملاحظات (اختياري)"></textarea>
                </div>

                <!-- Terms Section -->
                <div class="terms-section">
                    <label>الشروط</label>
                    <textarea id="terms" class="form-control" placeholder="أدخل الشروط والأحكام الخاصة بك. (نصيحة: إذا كانت الدفعة مستحقة بأدب، فإن فواتير FreshBooks التي تتضمن 'من فضلك' و 'شكرًا' تحصل على أموال أسرع بـ 2 يوم!)"></textarea>
                </div>
            </div>

            <!-- Settings Sidebar -->
            <div class="settings-sidebar">
                <h3>الإعدادات</h3>
                <p class="sidebar-subtitle">لهذه الفاتورة</p>

                <div class="setting-item">
                    <div class="setting-header">
                        <i class="fas fa-credit-card"></i>
                        <span>قبول المدفوعات الإلكترونية</span>
                        <span class="setting-status">لا</span>
                        <i class="fas fa-chevron-left"></i>
                    </div>
                    <p class="setting-description">دع العملاء يدفعون عبر الإنترنت</p>
                    <div class="payment-methods">
                        <img src="https://via.placeholder.com/30x20/0052CC/FFFFFF?text=VISA" alt="Visa">
                        <img src="https://via.placeholder.com/30x20/EB001B/FFFFFF?text=MC" alt="Mastercard">
                        <img src="https://via.placeholder.com/30x20/003087/FFFFFF?text=AE" alt="American Express">
                        <span>+4</span>
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-header">
                        <i class="fas fa-palette"></i>
                        <span>تخصيص نمط الفاتورة</span>
                        <i class="fas fa-chevron-left"></i>
                    </div>
                    <p class="setting-description">تغيير القالب واللون والخط</p>
                </div>

                <div class="setting-item">
                    <div class="setting-header">
                        <i class="fas fa-sync-alt"></i>
                        <span>جعل متكررة</span>
                        <i class="fas fa-chevron-left"></i>
                    </div>
                    <p class="setting-description">إرسال فاتورة للعملاء تلقائياً</p>
                </div>
            </div>
        </div>

        <!-- Attachments Section -->
        <div class="attachments-section">
            <h3>المرفقات</h3>
            <div class="attachment-upload" id="attachmentUpload">
                <i class="fas fa-plus"></i>
                <span>إضافة مرفق</span>
                <input type="file" id="attachmentFile" multiple style="display: none;">
            </div>
        </div>
    </div>

    <!-- Client Modal -->
    <div id="clientModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>إضافة عميل جديد</h3>
                <span class="close" onclick="closeClientModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="clientForm">
                    <div class="form-group">
                        <label>اسم العميل</label>
                        <input type="text" id="clientName" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label>البريد الإلكتروني</label>
                        <input type="email" id="clientEmail" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>رقم الهاتف</label>
                        <input type="tel" id="clientPhone" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>العنوان</label>
                        <textarea id="clientAddress" class="form-control"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeClientModal()">إلغاء</button>
                <button class="btn btn-primary" onclick="saveClient()">حفظ العميل</button>
            </div>
        </div>
    </div>

    <script src="js/script.js"></script>
    <script>
        function toggleUserMenu() {
            const dropdown = document.getElementById('userDropdown');
            dropdown.style.display = dropdown.style.display === 'none' ? 'block' : 'none';
        }

        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', function(event) {
            const userMenu = document.querySelector('.user-menu');
            const dropdown = document.getElementById('userDropdown');

            if (!userMenu.contains(event.target)) {
                dropdown.style.display = 'none';
            }
        });
    </script>
</body>
</html>
