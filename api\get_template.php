<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../includes/middleware.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً']);
    exit();
}

$templateId = $_GET['id'] ?? null;

if (!$templateId) {
    echo json_encode(['success' => false, 'message' => 'معرف القالب مطلوب']);
    exit();
}

try {
    $query = "SELECT * FROM invoice_templates WHERE id = ? AND is_active = 1";
    $stmt = $db->prepare($query);
    $stmt->execute([$templateId]);
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($template) {
        echo json_encode([
            'success' => true,
            'template' => $template
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'القالب غير موجود'
        ]);
    }
    
} catch (Exception $e) {
    Config::logError("Error getting template: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ أثناء تحميل القالب'
    ]);
}
?>
