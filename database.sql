-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS invoice_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE invoice_system;

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT 'اسم المستخدم',
    email VARCHAR(255) UNIQUE NOT NULL COMMENT 'البريد الإلكتروني',
    password_hash VARCHAR(255) NOT NULL COMMENT 'كلمة المرور المشفرة',
    full_name VARCHAR(255) NOT NULL COMMENT 'الاسم الكامل',
    company_name VARCHAR(255) COMMENT 'اسم الشركة',
    phone VARCHAR(50) COMMENT 'رقم الهاتف',
    address TEXT COMMENT 'العنوان',
    user_type ENUM('admin', 'user') DEFAULT 'user' COMMENT 'نوع المستخدم',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT 'حالة المستخدم',
    email_verified BOOLEAN DEFAULT FALSE COMMENT 'تأكيد البريد الإلكتروني',
    last_login TIMESTAMP NULL COMMENT 'آخر تسجيل دخول',
    login_attempts INT DEFAULT 0 COMMENT 'محاولات تسجيل الدخول الفاشلة',
    locked_until TIMESTAMP NULL COMMENT 'مقفل حتى',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ التحديث',
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status (status),
    INDEX idx_user_type (user_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول المستخدمين';

-- جدول الجلسات
CREATE TABLE IF NOT EXISTS sessions (
    id VARCHAR(128) PRIMARY KEY COMMENT 'معرف الجلسة',
    user_id INT NOT NULL COMMENT 'معرف المستخدم',
    ip_address VARCHAR(45) COMMENT 'عنوان IP',
    user_agent TEXT COMMENT 'معلومات المتصفح',
    payload TEXT COMMENT 'بيانات الجلسة',
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'آخر نشاط',
    expires_at TIMESTAMP NOT NULL COMMENT 'تاريخ انتهاء الصلاحية',
    remember_token VARCHAR(100) COMMENT 'رمز التذكر',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_last_activity (last_activity),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الجلسات';

-- جدول رموز إعادة تعيين كلمة المرور
CREATE TABLE IF NOT EXISTS password_resets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL COMMENT 'البريد الإلكتروني',
    token VARCHAR(255) NOT NULL COMMENT 'رمز إعادة التعيين',
    expires_at TIMESTAMP NOT NULL COMMENT 'تاريخ انتهاء الصلاحية',
    used BOOLEAN DEFAULT FALSE COMMENT 'تم الاستخدام',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    INDEX idx_email (email),
    INDEX idx_token (token),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول رموز إعادة تعيين كلمة المرور';

-- جدول العملاء
CREATE TABLE IF NOT EXISTS clients (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT 'معرف المستخدم المالك',
    name VARCHAR(255) NOT NULL COMMENT 'اسم العميل',
    email VARCHAR(255) COMMENT 'البريد الإلكتروني',
    phone VARCHAR(50) COMMENT 'رقم الهاتف',
    address TEXT COMMENT 'العنوان',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ التحديث',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_name (name),
    INDEX idx_email (email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول العملاء';

-- جدول الفواتير
CREATE TABLE IF NOT EXISTS invoices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT 'معرف المستخدم المالك',
    invoice_number VARCHAR(50) NOT NULL COMMENT 'رقم الفاتورة',
    client_id INT COMMENT 'معرف العميل',
    issue_date DATE NOT NULL COMMENT 'تاريخ الإصدار',
    due_date DATE COMMENT 'تاريخ الاستحقاق',
    reference_number VARCHAR(100) COMMENT 'رقم المرجع',
    subtotal DECIMAL(10,2) DEFAULT 0 COMMENT 'المجموع الفرعي',
    discount_percentage DECIMAL(5,2) DEFAULT 0 COMMENT 'نسبة الخصم',
    discount_amount DECIMAL(10,2) DEFAULT 0 COMMENT 'مبلغ الخصم',
    tax_rate DECIMAL(5,2) DEFAULT 0 COMMENT 'نسبة الضريبة',
    tax_amount DECIMAL(10,2) DEFAULT 0 COMMENT 'مبلغ الضريبة',
    total_amount DECIMAL(10,2) DEFAULT 0 COMMENT 'المبلغ الإجمالي',
    amount_paid DECIMAL(10,2) DEFAULT 0 COMMENT 'المبلغ المدفوع',
    amount_due DECIMAL(10,2) DEFAULT 0 COMMENT 'المبلغ المستحق',
    notes TEXT COMMENT 'ملاحظات',
    terms TEXT COMMENT 'الشروط والأحكام',
    status ENUM('draft', 'sent', 'paid', 'overdue') DEFAULT 'draft' COMMENT 'حالة الفاتورة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ التحديث',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE SET NULL,
    UNIQUE KEY unique_invoice_per_user (user_id, invoice_number),
    INDEX idx_user_id (user_id),
    INDEX idx_invoice_number (invoice_number),
    INDEX idx_client_id (client_id),
    INDEX idx_status (status),
    INDEX idx_issue_date (issue_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الفواتير';

-- جدول عناصر الفاتورة
CREATE TABLE IF NOT EXISTS invoice_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT NOT NULL COMMENT 'معرف الفاتورة',
    item_name VARCHAR(255) NOT NULL COMMENT 'اسم العنصر',
    item_description TEXT COMMENT 'وصف العنصر',
    quantity DECIMAL(10,2) NOT NULL DEFAULT 1 COMMENT 'الكمية',
    rate DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT 'السعر',
    amount DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT 'المبلغ',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    INDEX idx_invoice_id (invoice_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول عناصر الفاتورة';

-- جدول إعدادات الشركة
CREATE TABLE IF NOT EXISTS company_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_name VARCHAR(255) COMMENT 'اسم الشركة',
    company_id VARCHAR(100) COMMENT 'رقم تسجيل الشركة',
    country VARCHAR(100) COMMENT 'البلد',
    address TEXT COMMENT 'العنوان',
    phone VARCHAR(50) COMMENT 'رقم الهاتف',
    email VARCHAR(255) COMMENT 'البريد الإلكتروني',
    logo_path VARCHAR(255) COMMENT 'مسار الشعار',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ التحديث'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول إعدادات الشركة';

-- جدول المرفقات
CREATE TABLE IF NOT EXISTS attachments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT NOT NULL COMMENT 'معرف الفاتورة',
    file_name VARCHAR(255) NOT NULL COMMENT 'اسم الملف',
    file_path VARCHAR(500) NOT NULL COMMENT 'مسار الملف',
    file_size INT COMMENT 'حجم الملف بالبايت',
    file_type VARCHAR(100) COMMENT 'نوع الملف',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    INDEX idx_invoice_id (invoice_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول المرفقات';

-- جدول سجل الأنشطة
CREATE TABLE IF NOT EXISTS activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT 'معرف المستخدم',
    action VARCHAR(100) NOT NULL COMMENT 'نوع العملية',
    resource_type VARCHAR(50) COMMENT 'نوع المورد',
    resource_id INT COMMENT 'معرف المورد',
    details TEXT COMMENT 'تفاصيل العملية',
    ip_address VARCHAR(45) COMMENT 'عنوان IP',
    user_agent TEXT COMMENT 'معلومات المتصفح',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ العملية',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_resource (resource_type, resource_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول سجل الأنشطة';

-- جدول قوالب الفواتير
CREATE TABLE IF NOT EXISTS invoice_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT 'اسم القالب',
    description TEXT COMMENT 'وصف القالب',
    category ENUM('classic', 'modern', 'creative', 'minimal', 'professional') DEFAULT 'classic' COMMENT 'تصنيف القالب',
    color_scheme VARCHAR(50) NOT NULL COMMENT 'نظام الألوان',
    css_variables TEXT COMMENT 'متغيرات CSS للقالب',
    preview_image VARCHAR(255) COMMENT 'صورة المعاينة',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'القالب نشط',
    sort_order INT DEFAULT 0 COMMENT 'ترتيب العرض',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    INDEX idx_category (category),
    INDEX idx_color_scheme (color_scheme),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول قوالب الفواتير';

-- جدول تفضيلات المستخدمين للقوالب
CREATE TABLE IF NOT EXISTS user_template_preferences (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT 'معرف المستخدم',
    template_id INT NOT NULL COMMENT 'معرف القالب',
    is_default BOOLEAN DEFAULT FALSE COMMENT 'القالب الافتراضي',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ التحديث',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES invoice_templates(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_default (user_id, is_default),
    INDEX idx_user_id (user_id),
    INDEX idx_template_id (template_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول تفضيلات المستخدمين للقوالب';

-- إدراج بيانات تجريبية للشركة
INSERT INTO company_settings (company_name, company_id, country, address, phone, email) VALUES
('شركة الفواتير المحدودة', '0504280365', 'المملكة العربية السعودية', 'الرياض، المملكة العربية السعودية', '+966504280365', '<EMAIL>')
ON DUPLICATE KEY UPDATE company_name = company_name;

-- إدراج مستخدم تجريبي (كلمة المرور: 123456)
INSERT INTO users (username, email, password_hash, full_name, company_name, phone, address, user_type, status, email_verified) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'شركة الفواتير المحدودة', '+966504280365', 'الرياض، المملكة العربية السعودية', 'admin', 'active', TRUE),
('user1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'أحمد محمد الأحمد', 'شركة التجارة الحديثة', '+966501234567', 'جدة، المملكة العربية السعودية', 'user', 'active', TRUE)
ON DUPLICATE KEY UPDATE username = username;

-- إدراج بيانات تجريبية للعملاء (مرتبطة بالمستخدم التجريبي)
INSERT INTO clients (user_id, name, email, phone, address) VALUES
(2, 'أحمد محمد', '<EMAIL>', '+966501234567', 'الرياض، المملكة العربية السعودية'),
(2, 'فاطمة علي', '<EMAIL>', '+966507654321', 'جدة، المملكة العربية السعودية'),
(2, 'محمد سالم', '<EMAIL>', '+966509876543', 'الدمام، المملكة العربية السعودية')
ON DUPLICATE KEY UPDATE name = name;

-- إدراج فاتورة تجريبية (مرتبطة بالمستخدم التجريبي)
INSERT INTO invoices (user_id, invoice_number, client_id, issue_date, due_date, reference_number, subtotal, tax_rate, tax_amount, total_amount, amount_due, notes, terms, status) VALUES
(2, '000001', 1, CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY), 'REF-001', 1000.00, 15.00, 150.00, 1150.00, 1150.00, 'شكراً لتعاملكم معنا', 'الدفع خلال 30 يوم من تاريخ الفاتورة', 'sent')
ON DUPLICATE KEY UPDATE invoice_number = invoice_number;

-- إدراج عناصر الفاتورة التجريبية
INSERT INTO invoice_items (invoice_id, item_name, item_description, quantity, rate, amount) VALUES
(1, 'خدمة استشارية', 'استشارة تقنية لمدة ساعة', 2, 300.00, 600.00),
(1, 'تطوير موقع', 'تطوير موقع إلكتروني بسيط', 1, 400.00, 400.00)
ON DUPLICATE KEY UPDATE item_name = item_name;

-- إدراج قوالب الفواتير
INSERT INTO invoice_templates (name, description, category, color_scheme, css_variables, sort_order) VALUES
('الكلاسيكي الأزرق', 'قالب كلاسيكي بألوان زرقاء هادئة', 'classic', 'blue', '{"primary": "#2c3e50", "secondary": "#3498db", "accent": "#e3f2fd", "text": "#2c3e50", "background": "#ffffff"}', 1),
('العصري الأخضر', 'قالب عصري بألوان خضراء منعشة', 'modern', 'green', '{"primary": "#27ae60", "secondary": "#2ecc71", "accent": "#e8f5e8", "text": "#2c3e50", "background": "#ffffff"}', 2),
('الإبداعي البنفسجي', 'قالب إبداعي بألوان بنفسجية جذابة', 'creative', 'purple', '{"primary": "#8e44ad", "secondary": "#9b59b6", "accent": "#f4ecf7", "text": "#2c3e50", "background": "#ffffff"}', 3),
('المينيمال الرمادي', 'قالب بسيط وأنيق بألوان رمادية', 'minimal', 'gray', '{"primary": "#34495e", "secondary": "#95a5a6", "accent": "#ecf0f1", "text": "#2c3e50", "background": "#ffffff"}', 4),
('الاحترافي الأحمر', 'قالب احترافي بألوان حمراء قوية', 'professional', 'red', '{"primary": "#c0392b", "secondary": "#e74c3c", "accent": "#fadbd8", "text": "#2c3e50", "background": "#ffffff"}', 5),
('الكلاسيكي البرتقالي', 'قالب كلاسيكي بألوان برتقالية دافئة', 'classic', 'orange', '{"primary": "#d35400", "secondary": "#e67e22", "accent": "#fdeaa7", "text": "#2c3e50", "background": "#ffffff"}', 6),
('العصري الفيروزي', 'قالب عصري بألوان فيروزية مميزة', 'modern', 'teal', '{"primary": "#16a085", "secondary": "#1abc9c", "accent": "#d5f4e6", "text": "#2c3e50", "background": "#ffffff"}', 7),
('الإبداعي الوردي', 'قالب إبداعي بألوان وردية ناعمة', 'creative', 'pink', '{"primary": "#ad1457", "secondary": "#e91e63", "accent": "#fce4ec", "text": "#2c3e50", "background": "#ffffff"}', 8),
('المينيمال الأسود', 'قالب مينيمال بألوان سوداء أنيقة', 'minimal', 'black', '{"primary": "#2c3e50", "secondary": "#34495e", "accent": "#ecf0f1", "text": "#2c3e50", "background": "#ffffff"}', 9),
('الاحترافي الذهبي', 'قالب احترافي بألوان ذهبية فاخرة', 'professional', 'gold', '{"primary": "#b7950b", "secondary": "#f1c40f", "accent": "#fcf3cf", "text": "#2c3e50", "background": "#ffffff"}', 10),
('الكلاسيكي البحري', 'قالب كلاسيكي بألوان بحرية هادئة', 'classic', 'navy', '{"primary": "#1b4f72", "secondary": "#2874a6", "accent": "#d6eaf8", "text": "#2c3e50", "background": "#ffffff"}', 11),
('العصري الليموني', 'قالب عصري بألوان ليمونية منعشة', 'modern', 'lime', '{"primary": "#7d8471", "secondary": "#a9b388", "accent": "#f7f8f3", "text": "#2c3e50", "background": "#ffffff"}', 12),
('الإبداعي المتدرج', 'قالب إبداعي بألوان متدرجة جميلة', 'creative', 'gradient', '{"primary": "#667eea", "secondary": "#764ba2", "accent": "#f8f9ff", "text": "#2c3e50", "background": "#ffffff"}', 13),
('المينيمال الأبيض', 'قالب مينيمال بألوان بيضاء نظيفة', 'minimal', 'white', '{"primary": "#2c3e50", "secondary": "#7f8c8d", "accent": "#f8f9fa", "text": "#2c3e50", "background": "#ffffff"}', 14),
('الاحترافي الأزرق الداكن', 'قالب احترافي بألوان زرقاء داكنة', 'professional', 'dark-blue', '{"primary": "#1a252f", "secondary": "#2c3e50", "accent": "#d5dbdb", "text": "#2c3e50", "background": "#ffffff"}', 15),
('الكلاسيكي البني', 'قالب كلاسيكي بألوان بنية دافئة', 'classic', 'brown', '{"primary": "#6d4c41", "secondary": "#8d6e63", "accent": "#efebe9", "text": "#2c3e50", "background": "#ffffff"}', 16),
('العصري النيون', 'قالب عصري بألوان نيون مشرقة', 'modern', 'neon', '{"primary": "#00bcd4", "secondary": "#4dd0e1", "accent": "#e0f2f1", "text": "#2c3e50", "background": "#ffffff"}', 17),
('الإبداعي الغروب', 'قالب إبداعي بألوان الغروب الدافئة', 'creative', 'sunset', '{"primary": "#ff6b35", "secondary": "#f7931e", "accent": "#fff3e0", "text": "#2c3e50", "background": "#ffffff"}', 18),
('المينيمال الباستيل', 'قالب مينيمال بألوان باستيل هادئة', 'minimal', 'pastel', '{"primary": "#81c784", "secondary": "#a5d6a7", "accent": "#f1f8e9", "text": "#2c3e50", "background": "#ffffff"}', 19),
('الاحترافي الفضي', 'قالب احترافي بألوان فضية راقية', 'professional', 'silver', '{"primary": "#546e7a", "secondary": "#78909c", "accent": "#eceff1", "text": "#2c3e50", "background": "#ffffff"}', 20)
ON DUPLICATE KEY UPDATE name = name;

-- إنشاء فهارس إضافية لتحسين الأداء
CREATE INDEX idx_clients_name ON clients(name);
CREATE INDEX idx_clients_email ON clients(email);
CREATE INDEX idx_invoices_dates ON invoices(issue_date, due_date);
CREATE INDEX idx_invoice_items_name ON invoice_items(item_name);

-- إنشاء view لعرض الفواتير مع معلومات العملاء
CREATE OR REPLACE VIEW invoice_summary AS
SELECT 
    i.id,
    i.invoice_number,
    i.issue_date,
    i.due_date,
    i.total_amount,
    i.amount_paid,
    i.amount_due,
    i.status,
    c.name as client_name,
    c.email as client_email,
    c.phone as client_phone,
    CASE 
        WHEN i.status = 'paid' THEN 'مدفوعة'
        WHEN i.status = 'sent' THEN 'مرسلة'
        WHEN i.status = 'overdue' THEN 'متأخرة'
        ELSE 'مسودة'
    END as status_arabic,
    DATEDIFF(CURDATE(), i.due_date) as days_overdue
FROM invoices i
LEFT JOIN clients c ON i.client_id = c.id
ORDER BY i.created_at DESC;

-- إنشاء stored procedure لحساب إجماليات الفاتورة
DELIMITER //
CREATE PROCEDURE CalculateInvoiceTotals(IN invoice_id INT)
BEGIN
    DECLARE subtotal DECIMAL(10,2) DEFAULT 0;
    DECLARE discount_amount DECIMAL(10,2) DEFAULT 0;
    DECLARE tax_amount DECIMAL(10,2) DEFAULT 0;
    DECLARE total_amount DECIMAL(10,2) DEFAULT 0;
    DECLARE amount_due DECIMAL(10,2) DEFAULT 0;
    DECLARE discount_percentage DECIMAL(5,2) DEFAULT 0;
    DECLARE tax_rate DECIMAL(5,2) DEFAULT 0;
    DECLARE amount_paid DECIMAL(10,2) DEFAULT 0;
    
    -- حساب المجموع الفرعي
    SELECT COALESCE(SUM(amount), 0) INTO subtotal
    FROM invoice_items 
    WHERE invoice_items.invoice_id = invoice_id;
    
    -- الحصول على نسب الخصم والضريبة والمبلغ المدفوع
    SELECT 
        COALESCE(discount_percentage, 0),
        COALESCE(tax_rate, 0),
        COALESCE(amount_paid, 0)
    INTO discount_percentage, tax_rate, amount_paid
    FROM invoices 
    WHERE id = invoice_id;
    
    -- حساب مبلغ الخصم
    SET discount_amount = subtotal * (discount_percentage / 100);
    
    -- حساب مبلغ الضريبة
    SET tax_amount = (subtotal - discount_amount) * (tax_rate / 100);
    
    -- حساب المبلغ الإجمالي
    SET total_amount = subtotal - discount_amount + tax_amount;
    
    -- حساب المبلغ المستحق
    SET amount_due = total_amount - amount_paid;
    
    -- تحديث الفاتورة
    UPDATE invoices 
    SET 
        subtotal = subtotal,
        discount_amount = discount_amount,
        tax_amount = tax_amount,
        total_amount = total_amount,
        amount_due = amount_due
    WHERE id = invoice_id;
END //
DELIMITER ;

-- إنشاء trigger لحساب الإجماليات تلقائياً عند إضافة أو تعديل عناصر الفاتورة
DELIMITER //
CREATE TRIGGER update_invoice_totals_after_insert
AFTER INSERT ON invoice_items
FOR EACH ROW
BEGIN
    CALL CalculateInvoiceTotals(NEW.invoice_id);
END //

CREATE TRIGGER update_invoice_totals_after_update
AFTER UPDATE ON invoice_items
FOR EACH ROW
BEGIN
    CALL CalculateInvoiceTotals(NEW.invoice_id);
END //

CREATE TRIGGER update_invoice_totals_after_delete
AFTER DELETE ON invoice_items
FOR EACH ROW
BEGIN
    CALL CalculateInvoiceTotals(OLD.invoice_id);
END //
DELIMITER ;

-- إنشاء function لتوليد رقم فاتورة جديد
DELIMITER //
CREATE FUNCTION GenerateInvoiceNumber() RETURNS VARCHAR(50)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE next_number INT DEFAULT 1;
    
    SELECT COALESCE(MAX(CAST(SUBSTRING(invoice_number, 1) AS UNSIGNED)), 0) + 1 
    INTO next_number
    FROM invoices;
    
    RETURN LPAD(next_number, 6, '0');
END //
DELIMITER ;
