-- إعد<PERSON> جداول نظام القوالب
USE invoice_system;

-- جدول قوالب الفواتير
CREATE TABLE IF NOT EXISTS invoice_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT 'اسم القالب',
    description TEXT COMMENT 'وصف القالب',
    category ENUM('classic', 'modern', 'creative', 'minimal', 'professional') DEFAULT 'classic' COMMENT 'تصنيف القالب',
    color_scheme VARCHAR(50) NOT NULL COMMENT 'نظام الألوان',
    css_variables TEXT COMMENT 'متغيرات CSS للقالب',
    preview_image VARCHAR(255) COMMENT 'صورة المعاينة',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'القالب نشط',
    sort_order INT DEFAULT 0 COMMENT 'ترتيب العرض',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    INDEX idx_category (category),
    INDEX idx_color_scheme (color_scheme),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول قوالب الفواتير';

-- جدول تفضيلات المستخدمين للقوالب
CREATE TABLE IF NOT EXISTS user_template_preferences (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT 'معرف المستخدم',
    template_id INT NOT NULL COMMENT 'معرف القالب',
    is_default BOOLEAN DEFAULT FALSE COMMENT 'القالب الافتراضي',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ التحديث',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES invoice_templates(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_default (user_id, is_default),
    INDEX idx_user_id (user_id),
    INDEX idx_template_id (template_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول تفضيلات المستخدمين للقوالب';

-- حذف البيانات الموجودة (إن وجدت) وإدراج القوالب الجديدة
DELETE FROM invoice_templates;

-- إدراج قوالب الفواتير
INSERT INTO invoice_templates (name, description, category, color_scheme, css_variables, sort_order) VALUES
('الكلاسيكي الأزرق', 'قالب كلاسيكي بألوان زرقاء هادئة', 'classic', 'blue', '{"primary": "#2c3e50", "secondary": "#3498db", "accent": "#e3f2fd", "text": "#2c3e50", "background": "#ffffff"}', 1),
('العصري الأخضر', 'قالب عصري بألوان خضراء منعشة', 'modern', 'green', '{"primary": "#27ae60", "secondary": "#2ecc71", "accent": "#e8f5e8", "text": "#2c3e50", "background": "#ffffff"}', 2),
('الإبداعي البنفسجي', 'قالب إبداعي بألوان بنفسجية جذابة', 'creative', 'purple', '{"primary": "#8e44ad", "secondary": "#9b59b6", "accent": "#f4ecf7", "text": "#2c3e50", "background": "#ffffff"}', 3),
('المينيمال الرمادي', 'قالب بسيط وأنيق بألوان رمادية', 'minimal', 'gray', '{"primary": "#34495e", "secondary": "#95a5a6", "accent": "#ecf0f1", "text": "#2c3e50", "background": "#ffffff"}', 4),
('الاحترافي الأحمر', 'قالب احترافي بألوان حمراء قوية', 'professional', 'red', '{"primary": "#c0392b", "secondary": "#e74c3c", "accent": "#fadbd8", "text": "#2c3e50", "background": "#ffffff"}', 5),
('الكلاسيكي البرتقالي', 'قالب كلاسيكي بألوان برتقالية دافئة', 'classic', 'orange', '{"primary": "#d35400", "secondary": "#e67e22", "accent": "#fdeaa7", "text": "#2c3e50", "background": "#ffffff"}', 6),
('العصري الفيروزي', 'قالب عصري بألوان فيروزية مميزة', 'modern', 'teal', '{"primary": "#16a085", "secondary": "#1abc9c", "accent": "#d5f4e6", "text": "#2c3e50", "background": "#ffffff"}', 7),
('الإبداعي الوردي', 'قالب إبداعي بألوان وردية ناعمة', 'creative', 'pink', '{"primary": "#ad1457", "secondary": "#e91e63", "accent": "#fce4ec", "text": "#2c3e50", "background": "#ffffff"}', 8),
('المينيمال الأسود', 'قالب مينيمال بألوان سوداء أنيقة', 'minimal', 'black', '{"primary": "#2c3e50", "secondary": "#34495e", "accent": "#ecf0f1", "text": "#2c3e50", "background": "#ffffff"}', 9),
('الاحترافي الذهبي', 'قالب احترافي بألوان ذهبية فاخرة', 'professional', 'gold', '{"primary": "#b7950b", "secondary": "#f1c40f", "accent": "#fcf3cf", "text": "#2c3e50", "background": "#ffffff"}', 10),
('الكلاسيكي البحري', 'قالب كلاسيكي بألوان بحرية هادئة', 'classic', 'navy', '{"primary": "#1b4f72", "secondary": "#2874a6", "accent": "#d6eaf8", "text": "#2c3e50", "background": "#ffffff"}', 11),
('العصري الليموني', 'قالب عصري بألوان ليمونية منعشة', 'modern', 'lime', '{"primary": "#7d8471", "secondary": "#a9b388", "accent": "#f7f8f3", "text": "#2c3e50", "background": "#ffffff"}', 12),
('الإبداعي المتدرج', 'قالب إبداعي بألوان متدرجة جميلة', 'creative', 'gradient', '{"primary": "#667eea", "secondary": "#764ba2", "accent": "#f8f9ff", "text": "#2c3e50", "background": "#ffffff"}', 13),
('المينيمال الأبيض', 'قالب مينيمال بألوان بيضاء نظيفة', 'minimal', 'white', '{"primary": "#2c3e50", "secondary": "#7f8c8d", "accent": "#f8f9fa", "text": "#2c3e50", "background": "#ffffff"}', 14),
('الاحترافي الأزرق الداكن', 'قالب احترافي بألوان زرقاء داكنة', 'professional', 'dark-blue', '{"primary": "#1a252f", "secondary": "#2c3e50", "accent": "#d5dbdb", "text": "#2c3e50", "background": "#ffffff"}', 15),
('الكلاسيكي البني', 'قالب كلاسيكي بألوان بنية دافئة', 'classic', 'brown', '{"primary": "#6d4c41", "secondary": "#8d6e63", "accent": "#efebe9", "text": "#2c3e50", "background": "#ffffff"}', 16),
('العصري النيون', 'قالب عصري بألوان نيون مشرقة', 'modern', 'neon', '{"primary": "#00bcd4", "secondary": "#4dd0e1", "accent": "#e0f2f1", "text": "#2c3e50", "background": "#ffffff"}', 17),
('الإبداعي الغروب', 'قالب إبداعي بألوان الغروب الدافئة', 'creative', 'sunset', '{"primary": "#ff6b35", "secondary": "#f7931e", "accent": "#fff3e0", "text": "#2c3e50", "background": "#ffffff"}', 18),
('المينيمال الباستيل', 'قالب مينيمال بألوان باستيل هادئة', 'minimal', 'pastel', '{"primary": "#81c784", "secondary": "#a5d6a7", "accent": "#f1f8e9", "text": "#2c3e50", "background": "#ffffff"}', 19),
('الاحترافي الفضي', 'قالب احترافي بألوان فضية راقية', 'professional', 'silver', '{"primary": "#546e7a", "secondary": "#78909c", "accent": "#eceff1", "text": "#2c3e50", "background": "#ffffff"}', 20);

-- التحقق من نجاح الإدراج
SELECT COUNT(*) as 'عدد القوالب المدرجة' FROM invoice_templates;

-- عرض أول 5 قوالب للتأكيد
SELECT id, name, category, color_scheme FROM invoice_templates ORDER BY sort_order LIMIT 5;
