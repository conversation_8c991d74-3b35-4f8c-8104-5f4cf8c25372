<?php
require_once 'includes/middleware.php';

// التحقق من تسجيل الدخول
requireAuth();

$user = getCurrentUser();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض الفاتورة - نظام إنشاء الفواتير</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/templates.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        .invoice-view {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .invoice-header-view {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .invoice-actions {
            display: flex;
            gap: 10px;
        }
        
        .invoice-paper {
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .invoice-header-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 40px;
            border-bottom: 2px solid #eee;
            padding-bottom: 20px;
        }
        
        .company-logo {
            width: 150px;
            height: 100px;
            background-color: #f8f9fa;
            border: 2px dashed #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
        }
        
        .company-details {
            text-align: right;
        }
        
        .invoice-title {
            font-size: 32px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .invoice-meta {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }
        
        .client-info, .invoice-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
        }
        
        .section-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 16px;
        }
        
        .info-item {
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .info-label {
            font-weight: 500;
            color: #6c757d;
            display: inline-block;
            width: 100px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .items-table th {
            background-color: #2c3e50;
            color: white;
            padding: 15px;
            text-align: right;
            font-weight: 600;
        }
        
        .items-table td {
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .items-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .item-description {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .invoice-summary {
            margin-right: auto;
            width: 300px;
        }
        
        .summary-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .summary-table td {
            padding: 10px 15px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .summary-table .total-row {
            background-color: #2c3e50;
            color: white;
            font-weight: 600;
            font-size: 18px;
        }
        
        .invoice-notes {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
        }
        
        .notes-section {
            margin-bottom: 20px;
        }
        
        .notes-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .notes-content {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .status-badge {
            padding: 6px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .status-draft {
            background-color: #ffc107;
            color: #856404;
        }
        
        .status-sent {
            background-color: #17a2b8;
            color: white;
        }
        
        .status-paid {
            background-color: #28a745;
            color: white;
        }
        
        .status-overdue {
            background-color: #dc3545;
            color: white;
        }
        
        @media print {
            .invoice-header-view,
            .invoice-actions {
                display: none !important;
            }
            
            .invoice-view {
                padding: 0;
                max-width: none;
            }
            
            .invoice-paper {
                box-shadow: none;
                margin: 0;
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-view">
        <div class="invoice-header-view">
            <h1>عرض الفاتورة</h1>
            <div class="invoice-actions">
                <button class="btn btn-secondary" onclick="window.history.back()">
                    <i class="fas fa-arrow-right"></i> رجوع
                </button>
                <button class="btn btn-primary" onclick="window.print()">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <button class="btn btn-success" onclick="downloadPDF()">
                    <i class="fas fa-download"></i> تحميل PDF
                </button>
            </div>
        </div>

        <div class="invoice-paper" id="invoicePaper">
            <!-- Invoice content will be loaded here -->
        </div>
    </div>

    <script>
        // Get invoice ID from URL
        const urlParams = new URLSearchParams(window.location.search);
        const invoiceId = urlParams.get('id');

        if (!invoiceId) {
            alert('معرف الفاتورة غير صحيح');
            window.location.href = 'invoices.php';
        } else {
            loadInvoice(invoiceId);
        }

        // Load invoice data
        function loadInvoice(id) {
            fetch(`api/invoices.php?id=${id}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayInvoice(data.data);
                    } else {
                        alert('خطأ: ' + data.message);
                        window.location.href = 'invoices.php';
                    }
                })
                .catch(error => {
                    console.error('Error loading invoice:', error);
                    alert('حدث خطأ أثناء تحميل الفاتورة');
                });
        }

        // Display invoice
        function displayInvoice(invoice) {
            const invoicePaper = document.getElementById('invoicePaper');
            
            // Update page title
            document.title = `فاتورة رقم ${invoice.invoice_number} - نظام إنشاء الفواتير`;
            
            let itemsHTML = '';
            if (invoice.items && invoice.items.length > 0) {
                invoice.items.forEach(item => {
                    itemsHTML += `
                        <tr>
                            <td>
                                <strong>${item.item_name}</strong>
                                ${item.item_description ? `<div class="item-description">${item.item_description}</div>` : ''}
                            </td>
                            <td>${parseFloat(item.rate).toFixed(2)} ر.س</td>
                            <td>${parseFloat(item.quantity).toFixed(0)}</td>
                            <td>${parseFloat(item.amount).toFixed(2)} ر.س</td>
                        </tr>
                    `;
                });
            }

            invoicePaper.innerHTML = `
                <div class="invoice-header-info">
                    <div class="company-logo">
                        <i class="fas fa-building" style="font-size: 24px; color: #6c757d;"></i>
                    </div>
                    <div class="company-details">
                        <div class="invoice-title">فاتورة</div>
                        <div style="font-size: 18px; font-weight: 600; margin-bottom: 5px;">
                            رقم الفاتورة: ${invoice.invoice_number}
                        </div>
                        <div style="margin-bottom: 5px;">0504280365</div>
                        <div style="margin-bottom: 10px;">المملكة العربية السعودية</div>
                        <span class="status-badge status-${invoice.status}">${getStatusText(invoice.status)}</span>
                    </div>
                </div>

                <div class="invoice-meta">
                    <div class="client-info">
                        <div class="section-title">معلومات العميل</div>
                        <div class="info-item">
                            <span class="info-label">الاسم:</span>
                            ${invoice.client_name || 'غير محدد'}
                        </div>
                        <div class="info-item">
                            <span class="info-label">البريد:</span>
                            ${invoice.client_email || '-'}
                        </div>
                        <div class="info-item">
                            <span class="info-label">الهاتف:</span>
                            ${invoice.client_phone || '-'}
                        </div>
                        ${invoice.client_address ? `
                        <div class="info-item">
                            <span class="info-label">العنوان:</span>
                            ${invoice.client_address}
                        </div>
                        ` : ''}
                    </div>
                    
                    <div class="invoice-info">
                        <div class="section-title">معلومات الفاتورة</div>
                        <div class="info-item">
                            <span class="info-label">تاريخ الإصدار:</span>
                            ${formatDate(invoice.issue_date)}
                        </div>
                        <div class="info-item">
                            <span class="info-label">تاريخ الاستحقاق:</span>
                            ${invoice.due_date ? formatDate(invoice.due_date) : '-'}
                        </div>
                        <div class="info-item">
                            <span class="info-label">المرجع:</span>
                            ${invoice.reference_number || '-'}
                        </div>
                    </div>
                </div>

                <table class="items-table">
                    <thead>
                        <tr>
                            <th>الوصف</th>
                            <th>السعر</th>
                            <th>الكمية</th>
                            <th>الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${itemsHTML || '<tr><td colspan="4" style="text-align: center; color: #6c757d;">لا توجد عناصر</td></tr>'}
                    </tbody>
                </table>

                <div class="invoice-summary">
                    <table class="summary-table">
                        <tr>
                            <td>المجموع الفرعي:</td>
                            <td>${parseFloat(invoice.subtotal || 0).toFixed(2)} ر.س</td>
                        </tr>
                        ${invoice.discount_amount > 0 ? `
                        <tr>
                            <td>الخصم (${invoice.discount_percentage}%):</td>
                            <td>-${parseFloat(invoice.discount_amount).toFixed(2)} ر.س</td>
                        </tr>
                        ` : ''}
                        <tr>
                            <td>الضريبة (${invoice.tax_rate}%):</td>
                            <td>${parseFloat(invoice.tax_amount || 0).toFixed(2)} ر.س</td>
                        </tr>
                        <tr class="total-row">
                            <td>الإجمالي:</td>
                            <td>${parseFloat(invoice.total_amount || 0).toFixed(2)} ر.س</td>
                        </tr>
                        <tr>
                            <td>المبلغ المدفوع:</td>
                            <td>${parseFloat(invoice.amount_paid || 0).toFixed(2)} ر.س</td>
                        </tr>
                        <tr style="background-color: #28a745; color: white; font-weight: 600;">
                            <td>المبلغ المستحق:</td>
                            <td>${parseFloat(invoice.amount_due || 0).toFixed(2)} ر.س</td>
                        </tr>
                    </table>
                </div>

                ${(invoice.notes || invoice.terms) ? `
                <div class="invoice-notes">
                    ${invoice.notes ? `
                    <div class="notes-section">
                        <div class="notes-title">ملاحظات:</div>
                        <div class="notes-content">${invoice.notes}</div>
                    </div>
                    ` : ''}
                    
                    ${invoice.terms ? `
                    <div class="notes-section">
                        <div class="notes-title">الشروط والأحكام:</div>
                        <div class="notes-content">${invoice.terms}</div>
                    </div>
                    ` : ''}
                </div>
                ` : ''}
            `;
        }

        // Format date
        function formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        }

        // Get status text in Arabic
        function getStatusText(status) {
            const statusMap = {
                'draft': 'مسودة',
                'sent': 'مرسلة',
                'paid': 'مدفوعة',
                'overdue': 'متأخرة'
            };
            return statusMap[status] || status;
        }

        // Download PDF (placeholder function)
        function downloadPDF() {
            // This would typically use a library like jsPDF or send to server for PDF generation
            alert('ميزة تحميل PDF ستكون متاحة قريباً');
        }
    </script>
    <script src="js/templates.js"></script>
</body>
</html>
