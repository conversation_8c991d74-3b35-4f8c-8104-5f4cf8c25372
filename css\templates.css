/* ملف CSS للقوالب المختلفة */

/* متغيرات افتراضية */
:root {
    --template-primary: #2c3e50;
    --template-secondary: #3498db;
    --template-accent: #e3f2fd;
    --template-text: #2c3e50;
    --template-background: #ffffff;
}

/* القالب الأساسي */
.template-applied {
    font-family: 'Cairo', sans-serif;
    color: var(--template-text);
    background-color: var(--template-background);
}

/* رأس الفاتورة مع القالب */
.template-applied .invoice-header {
    background: linear-gradient(135deg, var(--template-primary), var(--template-secondary));
    color: white;
    position: relative;
    overflow: hidden;
}

.template-applied .invoice-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="3" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
    opacity: 0.3;
}

/* معلومات الشركة مع القالب */
.template-applied .company-details {
    background: var(--template-accent);
    border: 1px solid var(--template-primary);
    border-right: 4px solid var(--template-primary);
}

.template-applied .invoice-number {
    color: var(--template-primary);
    border-bottom: 2px solid var(--template-primary);
}

/* تفاصيل العميل مع القالب */
.template-applied .client-section {
    background: var(--template-accent);
    border-right: 4px solid var(--template-secondary);
}

.template-applied .section-title {
    color: var(--template-primary);
}

/* جدول العناصر مع القالب */
.template-applied .items-table th {
    background: var(--template-primary);
    color: white;
}

.template-applied .items-table tbody tr:nth-child(even) {
    background: var(--template-accent);
}

.template-applied .items-table tbody tr:hover {
    background: rgba(0,0,0,0.02);
}

/* ملخص المبالغ مع القالب */
.template-applied .summary-table .total-row {
    background: var(--template-primary);
    color: white;
}

.template-applied .summary-table .label {
    color: var(--template-text);
}

/* الأزرار مع القالب */
.template-applied .btn-primary {
    background: linear-gradient(135deg, var(--template-primary), var(--template-secondary));
    border-color: var(--template-primary);
}

.template-applied .btn-primary:hover {
    background: var(--template-primary);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.template-applied .btn-secondary {
    border-color: var(--template-secondary);
    color: var(--template-secondary);
}

.template-applied .btn-secondary:hover {
    background: var(--template-secondary);
    color: white;
}

/* الملاحظات والشروط مع القالب */
.template-applied .notes-section {
    border-top: 2px solid var(--template-accent);
}

.template-applied .notes-content {
    background: var(--template-accent);
    border-right: 4px solid var(--template-secondary);
}

/* تخصيصات للفئات المختلفة */

/* القوالب الإبداعية */
.template-creative .invoice-header {
    background: linear-gradient(45deg, var(--template-primary), var(--template-secondary), var(--template-primary));
    background-size: 200% 200%;
    animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.template-creative .company-details {
    border-radius: 15px;
    position: relative;
    overflow: hidden;
}

.template-creative .company-details::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: shine 2s infinite;
}

@keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* القوالب المينيمال */
.template-minimal .invoice-header {
    background: var(--template-primary);
    padding: 30px 40px;
}

.template-minimal .company-details {
    border: 1px solid var(--template-primary);
    border-radius: 0;
}

.template-minimal .items-table {
    border: none;
    box-shadow: none;
}

.template-minimal .items-table th {
    border-bottom: 2px solid var(--template-primary);
    background: transparent;
    color: var(--template-primary);
}

.template-minimal .items-table td {
    border-bottom: 1px solid #eee;
}

/* القوالب الاحترافية */
.template-professional .invoice-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--template-secondary), var(--template-primary));
}

.template-professional .company-details {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-radius: 8px;
}

.template-professional .items-table {
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    border-radius: 8px;
    overflow: hidden;
}

/* القوالب العصرية */
.template-modern .invoice-header {
    background: linear-gradient(135deg, var(--template-primary) 0%, var(--template-secondary) 50%, var(--template-primary) 100%);
    position: relative;
}

.template-modern .invoice-header::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 20px solid transparent;
    border-right: 20px solid transparent;
    border-top: 20px solid var(--template-secondary);
}

.template-modern .company-details {
    border-radius: 12px;
    background: linear-gradient(135deg, var(--template-accent), white);
}

.template-modern .items-table {
    border-radius: 12px;
    overflow: hidden;
}

.template-modern .items-table th {
    background: linear-gradient(135deg, var(--template-primary), var(--template-secondary));
}

/* القوالب الكلاسيكية */
.template-classic .invoice-header {
    background: var(--template-primary);
    border-bottom: 3px solid var(--template-secondary);
}

.template-classic .company-details {
    border: 2px solid var(--template-primary);
    border-radius: 4px;
}

.template-classic .items-table {
    border: 1px solid var(--template-primary);
}

.template-classic .items-table th {
    background: var(--template-primary);
    border-bottom: 2px solid var(--template-secondary);
}

/* تأثيرات إضافية للطباعة */
@media print {
    .template-applied .invoice-header {
        background: var(--template-primary) !important;
        color: white !important;
    }
    
    .template-applied .company-details {
        border: 1px solid var(--template-primary) !important;
        background: white !important;
    }
    
    .template-applied .items-table th {
        background: var(--template-primary) !important;
        color: white !important;
    }
    
    /* إزالة التأثيرات المتحركة في الطباعة */
    .template-creative .invoice-header {
        animation: none !important;
        background: var(--template-primary) !important;
    }
    
    .template-creative .company-details::before {
        display: none !important;
    }
}

/* تخصيصات للشاشات الصغيرة */
@media (max-width: 768px) {
    .template-applied .invoice-header {
        padding: 20px;
    }
    
    .template-applied .company-details {
        margin: 10px 0;
        padding: 15px;
    }
    
    .template-applied .items-table {
        font-size: 12px;
    }
    
    .template-applied .items-table th,
    .template-applied .items-table td {
        padding: 8px;
    }
}

/* ألوان خاصة لكل نظام ألوان */

/* الأزرق */
.color-blue {
    --template-primary: #2c3e50;
    --template-secondary: #3498db;
    --template-accent: #e3f2fd;
}

/* الأخضر */
.color-green {
    --template-primary: #27ae60;
    --template-secondary: #2ecc71;
    --template-accent: #e8f5e8;
}

/* البنفسجي */
.color-purple {
    --template-primary: #8e44ad;
    --template-secondary: #9b59b6;
    --template-accent: #f4ecf7;
}

/* الأحمر */
.color-red {
    --template-primary: #c0392b;
    --template-secondary: #e74c3c;
    --template-accent: #fadbd8;
}

/* البرتقالي */
.color-orange {
    --template-primary: #d35400;
    --template-secondary: #e67e22;
    --template-accent: #fdeaa7;
}

/* الرمادي */
.color-gray {
    --template-primary: #34495e;
    --template-secondary: #95a5a6;
    --template-accent: #ecf0f1;
}

/* الفيروزي */
.color-teal {
    --template-primary: #16a085;
    --template-secondary: #1abc9c;
    --template-accent: #d5f4e6;
}

/* الوردي */
.color-pink {
    --template-primary: #ad1457;
    --template-secondary: #e91e63;
    --template-accent: #fce4ec;
}

/* الأسود */
.color-black {
    --template-primary: #2c3e50;
    --template-secondary: #34495e;
    --template-accent: #ecf0f1;
}

/* الذهبي */
.color-gold {
    --template-primary: #b7950b;
    --template-secondary: #f1c40f;
    --template-accent: #fcf3cf;
}

/* البحري */
.color-navy {
    --template-primary: #1b4f72;
    --template-secondary: #2874a6;
    --template-accent: #d6eaf8;
}

/* الليموني */
.color-lime {
    --template-primary: #7d8471;
    --template-secondary: #a9b388;
    --template-accent: #f7f8f3;
}

/* المتدرج */
.color-gradient {
    --template-primary: #667eea;
    --template-secondary: #764ba2;
    --template-accent: #f8f9ff;
}

/* الأبيض */
.color-white {
    --template-primary: #2c3e50;
    --template-secondary: #7f8c8d;
    --template-accent: #f8f9fa;
}

/* الأزرق الداكن */
.color-dark-blue {
    --template-primary: #1a252f;
    --template-secondary: #2c3e50;
    --template-accent: #d5dbdb;
}

/* البني */
.color-brown {
    --template-primary: #6d4c41;
    --template-secondary: #8d6e63;
    --template-accent: #efebe9;
}

/* النيون */
.color-neon {
    --template-primary: #00bcd4;
    --template-secondary: #4dd0e1;
    --template-accent: #e0f2f1;
}

/* الغروب */
.color-sunset {
    --template-primary: #ff6b35;
    --template-secondary: #f7931e;
    --template-accent: #fff3e0;
}

/* الباستيل */
.color-pastel {
    --template-primary: #81c784;
    --template-secondary: #a5d6a7;
    --template-accent: #f1f8e9;
}

/* الفضي */
.color-silver {
    --template-primary: #546e7a;
    --template-secondary: #78909c;
    --template-accent: #eceff1;
}
