// Global variables
let clients = [];
let invoiceItems = [];
let invoiceCounter = 1;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    loadClients();
    calculateTotal();
});

// Initialize event listeners
function initializeEventListeners() {
    // Logo upload
    const logoUpload = document.getElementById('logoUpload');
    const logoFile = document.getElementById('logoFile');
    
    logoUpload.addEventListener('click', () => logoFile.click());
    logoFile.addEventListener('change', handleLogoUpload);
    
    // Attachment upload
    const attachmentUpload = document.getElementById('attachmentUpload');
    const attachmentFile = document.getElementById('attachmentFile');
    
    attachmentUpload.addEventListener('click', () => attachmentFile.click());
    attachmentFile.addEventListener('change', handleAttachmentUpload);
    
    // Drag and drop for logo
    logoUpload.addEventListener('dragover', handleDragOver);
    logoUpload.addEventListener('drop', handleLogoDrop);
    
    // Auto-calculate totals when inputs change
    document.addEventListener('input', function(e) {
        if (e.target.classList.contains('item-rate') || 
            e.target.classList.contains('item-qty') ||
            e.target.id === 'discount' ||
            e.target.id === 'taxRate') {
            calculateTotal();
        }
    });
}

// Handle logo upload
function handleLogoUpload(event) {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
        // عرض معاينة فورية
        const reader = new FileReader();
        reader.onload = function(e) {
            const logoUpload = document.getElementById('logoUpload');
            logoUpload.innerHTML = `<img src="${e.target.result}" alt="Company Logo" style="max-width: 100%; max-height: 100%; object-fit: contain;">`;
        };
        reader.readAsDataURL(file);

        // رفع الملف إلى الخادم
        uploadLogo(file);
    }
}

// Upload logo to server
function uploadLogo(file) {
    const formData = new FormData();
    formData.append('logo', file);
    formData.append('type', 'logo');

    fetch('api/upload.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('Logo uploaded successfully:', data.data);
            // حفظ مسار الشعار لاستخدامه عند حفظ الفاتورة
            window.logoPath = data.data.file_path;
        } else {
            console.error('Logo upload failed:', data.message);
            alert('فشل في رفع الشعار: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error uploading logo:', error);
        alert('حدث خطأ أثناء رفع الشعار');
    });
}

// Handle attachment upload
function handleAttachmentUpload(event) {
    const files = event.target.files;
    const attachmentUpload = document.getElementById('attachmentUpload');

    if (files.length > 0) {
        // عرض قائمة الملفات
        let fileList = '<div class="attachment-list">';
        for (let i = 0; i < files.length; i++) {
            fileList += `
                <div class="attachment-item" data-file-index="${i}">
                    <i class="fas fa-file"></i>
                    <span>${files[i].name}</span>
                    <span class="file-size">(${formatFileSize(files[i].size)})</span>
                    <button onclick="removeAttachmentFromList(${i})" class="btn-remove">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
        }
        fileList += '</div>';
        attachmentUpload.innerHTML = fileList;

        // رفع الملفات إلى الخادم
        uploadAttachments(files);
    }
}

// Upload attachments to server
function uploadAttachments(files) {
    const formData = new FormData();

    for (let i = 0; i < files.length; i++) {
        formData.append('attachments[]', files[i]);
    }
    formData.append('type', 'multiple_attachments');

    fetch('api/upload.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('Attachments uploaded successfully:', data.data);
            // حفظ معلومات المرفقات
            window.attachments = data.data;
        } else {
            console.error('Attachments upload failed');
            alert('فشل في رفع بعض المرفقات');
        }
    })
    .catch(error => {
        console.error('Error uploading attachments:', error);
        alert('حدث خطأ أثناء رفع المرفقات');
    });
}

// Format file size
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Remove attachment from list (before upload)
function removeAttachmentFromList(index) {
    const attachmentItem = document.querySelector(`[data-file-index="${index}"]`);
    if (attachmentItem) {
        attachmentItem.remove();
    }
}

// Handle drag over
function handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.style.backgroundColor = '#e3f2fd';
}

// Handle logo drop
function handleLogoDrop(event) {
    event.preventDefault();
    event.currentTarget.style.backgroundColor = '#f8f9fa';
    
    const files = event.dataTransfer.files;
    if (files.length > 0 && files[0].type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = function(e) {
            event.currentTarget.innerHTML = `<img src="${e.target.result}" alt="Company Logo" style="max-width: 100%; max-height: 100%; object-fit: contain;">`;
        };
        reader.readAsDataURL(files[0]);
    }
}

// Add new line item
function addNewLine() {
    const tableBody = document.getElementById('itemsTableBody');
    const newRow = document.createElement('tr');
    newRow.className = 'item-row';
    newRow.innerHTML = `
        <td>
            <input type="text" class="form-control item-name" placeholder="أدخل اسم العنصر">
            <input type="text" class="form-control item-description" placeholder="أدخل وصف العنصر">
        </td>
        <td>
            <input type="number" class="form-control item-rate" placeholder="0.00" step="0.01" onchange="calculateLineTotal(this)">
            <small class="tax-note">شامل الضريبة</small>
        </td>
        <td>
            <input type="number" class="form-control item-qty" value="1" min="1" onchange="calculateLineTotal(this)">
        </td>
        <td>
            <span class="line-total">0.00 ر.س</span>
        </td>
        <td>
            <button class="btn-remove" onclick="removeItem(this)">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;
    tableBody.appendChild(newRow);
}

// Remove item
function removeItem(button) {
    const row = button.closest('tr');
    row.remove();
    calculateTotal();
}

// Calculate line total
function calculateLineTotal(input) {
    const row = input.closest('tr');
    const rate = parseFloat(row.querySelector('.item-rate').value) || 0;
    const qty = parseFloat(row.querySelector('.item-qty').value) || 0;
    const total = rate * qty;
    
    row.querySelector('.line-total').textContent = total.toFixed(2) + ' ر.س';
    calculateTotal();
}

// Calculate total amounts
function calculateTotal() {
    let subtotal = 0;
    
    // Calculate subtotal from all line items
    document.querySelectorAll('.item-row').forEach(row => {
        const rate = parseFloat(row.querySelector('.item-rate').value) || 0;
        const qty = parseFloat(row.querySelector('.item-qty').value) || 0;
        subtotal += rate * qty;
    });
    
    // Get discount and tax rate
    const discount = parseFloat(document.getElementById('discount').value) || 0;
    const taxRate = parseFloat(document.getElementById('taxRate').value) || 0;
    
    // Calculate amounts
    const discountAmount = subtotal * (discount / 100);
    const subtotalAfterDiscount = subtotal - discountAmount;
    const taxAmount = subtotalAfterDiscount * (taxRate / 100);
    const total = subtotalAfterDiscount + taxAmount;
    const amountPaid = 0; // This would come from payment records
    const amountDue = total - amountPaid;
    
    // Update display
    document.getElementById('subtotal').textContent = subtotal.toFixed(2);
    document.getElementById('taxAmount').textContent = taxAmount.toFixed(2);
    document.getElementById('total').textContent = total.toFixed(2);
    document.getElementById('amountPaid').textContent = amountPaid.toFixed(2);
    document.getElementById('finalAmountDue').textContent = amountDue.toFixed(2) + ' ر.س';
    document.getElementById('amountDue').value = amountDue.toFixed(2) + ' ر.س';
}

// Client management functions
function createClient() {
    document.getElementById('clientModal').style.display = 'block';
}

function closeClientModal() {
    document.getElementById('clientModal').style.display = 'none';
    document.getElementById('clientForm').reset();
}

function saveClient() {
    const name = document.getElementById('clientName').value;
    const email = document.getElementById('clientEmail').value;
    const phone = document.getElementById('clientPhone').value;
    const address = document.getElementById('clientAddress').value;

    if (!name) {
        alert('يرجى إدخال اسم العميل');
        return;
    }

    const clientData = {
        name: name,
        email: email,
        phone: phone,
        address: address
    };

    // Save to API
    fetch('api/clients.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(clientData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            clients.push(data.data);
            updateClientSelect();
            closeClientModal();

            // Select the newly added client
            document.getElementById('clientSelect').value = data.data.id;

            alert('تم إضافة العميل بنجاح');
        } else {
            alert('خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error saving client:', error);
        alert('حدث خطأ أثناء حفظ العميل');
    });
}

function loadClients() {
    // Load from API
    fetch('api/clients.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                clients = data.data;
                updateClientSelect();
            }
        })
        .catch(error => {
            console.error('Error loading clients:', error);
            // Fallback to localStorage
            const savedClients = localStorage.getItem('clients');
            if (savedClients) {
                clients = JSON.parse(savedClients);
                updateClientSelect();
            }
        });
}

function updateClientSelect() {
    const select = document.getElementById('clientSelect');
    select.innerHTML = '<option value="">اختر عميل</option>';
    
    clients.forEach(client => {
        const option = document.createElement('option');
        option.value = client.id;
        option.textContent = client.name;
        select.appendChild(option);
    });
}

// Invoice actions
function saveInvoice() {
    const invoiceData = collectInvoiceData();

    // Save to API
    fetch('api/invoices.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(invoiceData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم حفظ الفاتورة بنجاح!');
            // Optionally redirect to invoice list or clear form
        } else {
            alert('خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error saving invoice:', error);
        alert('حدث خطأ أثناء حفظ الفاتورة');
    });
}

function sendInvoice() {
    const invoiceData = collectInvoiceData();
    
    if (!invoiceData.clientId) {
        alert('يرجى اختيار عميل أولاً');
        return;
    }
    
    // Here you would typically send the invoice via email or other method
    alert('تم إرسال الفاتورة بنجاح!');
}

function cancelInvoice() {
    if (confirm('هل أنت متأكد من إلغاء الفاتورة؟ سيتم فقدان جميع البيانات المدخلة.')) {
        location.reload();
    }
}

function collectInvoiceData() {
    const items = [];
    let subtotal = 0;

    document.querySelectorAll('.item-row').forEach(row => {
        const name = row.querySelector('.item-name').value;
        const description = row.querySelector('.item-description').value;
        const rate = parseFloat(row.querySelector('.item-rate').value) || 0;
        const qty = parseFloat(row.querySelector('.item-qty').value) || 0;
        const amount = rate * qty;

        if (name || rate > 0) {
            items.push({
                name: name,
                description: description,
                rate: rate,
                quantity: qty,
                amount: amount
            });
            subtotal += amount;
        }
    });

    const discountPercentage = parseFloat(document.getElementById('discount').value) || 0;
    const discountAmount = subtotal * (discountPercentage / 100);
    const subtotalAfterDiscount = subtotal - discountAmount;
    const taxRate = parseFloat(document.getElementById('taxRate').value) || 0;
    const taxAmount = subtotalAfterDiscount * (taxRate / 100);
    const totalAmount = subtotalAfterDiscount + taxAmount;
    const amountPaid = 0; // This would come from payment records
    const amountDue = totalAmount - amountPaid;

    return {
        invoice_number: document.getElementById('invoiceNum').value,
        client_id: document.getElementById('clientSelect').value || null,
        issue_date: document.getElementById('issueDate').value,
        due_date: document.getElementById('dueDate').value || null,
        reference_number: document.getElementById('reference').value || null,
        items: items,
        subtotal: subtotal,
        discount_percentage: discountPercentage,
        discount_amount: discountAmount,
        tax_rate: taxRate,
        tax_amount: taxAmount,
        total_amount: totalAmount,
        amount_paid: amountPaid,
        amount_due: amountDue,
        notes: document.getElementById('notes').value || null,
        terms: document.getElementById('terms').value || null,
        status: 'draft'
    };
}

// Generate next invoice number
function generateInvoiceNumber() {
    const savedInvoices = JSON.parse(localStorage.getItem('invoices') || '[]');
    const nextNumber = (savedInvoices.length + 1).toString().padStart(6, '0');
    document.getElementById('invoiceNum').value = nextNumber;
    document.getElementById('invoiceNumber').textContent = nextNumber;
}

// Remove attachment
function removeAttachment(index) {
    // This would remove the attachment from the list
    // Implementation depends on how attachments are stored
    console.log('Remove attachment at index:', index);
}

// Initialize invoice number on load
document.addEventListener('DOMContentLoaded', function() {
    generateInvoiceNumber();
});

// Close modal when clicking outside
window.addEventListener('click', function(event) {
    const modal = document.getElementById('clientModal');
    if (event.target === modal) {
        closeClientModal();
    }
});
