<?php
// نظام المصادقة وإدارة الجلسات
session_start();

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../config/config.php';

class Auth {
    private $db;
    private $sessionTimeout = 3600; // ساعة واحدة
    private $maxLoginAttempts = 5;
    private $lockoutTime = 900; // 15 دقيقة

    public function __construct() {
        global $db;
        $this->db = $db;
        $this->sessionTimeout = SESSION_TIMEOUT;
        $this->maxLoginAttempts = MAX_LOGIN_ATTEMPTS;
        $this->lockoutTime = LOGIN_LOCKOUT_TIME;
    }

    /**
     * تسجيل دخول المستخدم
     */
    public function login($username, $password, $remember = false) {
        try {
            // التحقق من محاولات تسجيل الدخول
            if ($this->isAccountLocked($username)) {
                return [
                    'success' => false,
                    'message' => 'الحساب مقفل مؤقتاً بسبب محاولات تسجيل دخول فاشلة متعددة'
                ];
            }

            // البحث عن المستخدم
            $user = $this->getUserByUsernameOrEmail($username);
            
            if (!$user) {
                $this->recordFailedLogin($username);
                return [
                    'success' => false,
                    'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'
                ];
            }

            // التحقق من حالة المستخدم
            if ($user['status'] !== 'active') {
                return [
                    'success' => false,
                    'message' => 'الحساب غير نشط. يرجى التواصل مع الإدارة'
                ];
            }

            // التحقق من كلمة المرور
            if (!password_verify($password, $user['password_hash'])) {
                $this->recordFailedLogin($username);
                return [
                    'success' => false,
                    'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'
                ];
            }

            // إنشاء الجلسة
            $this->createSession($user, $remember);
            
            // تحديث آخر تسجيل دخول وإعادة تعيين محاولات الدخول
            $this->updateLastLogin($user['id']);
            $this->resetLoginAttempts($user['id']);

            return [
                'success' => true,
                'message' => 'تم تسجيل الدخول بنجاح',
                'user' => $this->sanitizeUserData($user)
            ];

        } catch (Exception $e) {
            Config::logError("Login error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء تسجيل الدخول'
            ];
        }
    }

    /**
     * تسجيل خروج المستخدم
     */
    public function logout() {
        try {
            // حذف الجلسة من قاعدة البيانات
            if (isset($_SESSION['session_id'])) {
                $this->deleteSession($_SESSION['session_id']);
            }

            // مسح بيانات الجلسة
            session_unset();
            session_destroy();

            // حذف كوكيز التذكر
            if (isset($_COOKIE['remember_token'])) {
                setcookie('remember_token', '', time() - 3600, '/');
            }

            return ['success' => true, 'message' => 'تم تسجيل الخروج بنجاح'];

        } catch (Exception $e) {
            Config::logError("Logout error: " . $e->getMessage());
            return ['success' => false, 'message' => 'حدث خطأ أثناء تسجيل الخروج'];
        }
    }

    /**
     * التحقق من تسجيل الدخول
     */
    public function isLoggedIn() {
        // التحقق من وجود بيانات الجلسة
        if (!isset($_SESSION['user_id']) || !isset($_SESSION['session_id'])) {
            return $this->checkRememberToken();
        }

        // التحقق من صحة الجلسة
        if (!$this->validateSession($_SESSION['session_id'])) {
            $this->logout();
            return false;
        }

        // تحديث آخر نشاط
        $this->updateSessionActivity($_SESSION['session_id']);
        
        return true;
    }

    /**
     * الحصول على بيانات المستخدم الحالي
     */
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }

        return $this->getUserById($_SESSION['user_id']);
    }

    /**
     * التحقق من الصلاحيات
     */
    public function hasPermission($permission) {
        $user = $this->getCurrentUser();
        if (!$user) return false;

        // المدير له جميع الصلاحيات
        if ($user['user_type'] === 'admin') {
            return true;
        }

        // يمكن إضافة منطق صلاحيات أكثر تعقيداً هنا
        return true;
    }

    /**
     * إنشاء جلسة جديدة
     */
    private function createSession($user, $remember = false) {
        $sessionId = $this->generateSessionId();
        $expiresAt = date('Y-m-d H:i:s', time() + $this->sessionTimeout);
        $rememberToken = $remember ? $this->generateRememberToken() : null;

        // حفظ الجلسة في قاعدة البيانات
        $query = "INSERT INTO sessions (id, user_id, ip_address, user_agent, expires_at, remember_token) 
                  VALUES (?, ?, ?, ?, ?, ?)";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute([
            $sessionId,
            $user['id'],
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? '',
            $expiresAt,
            $rememberToken
        ]);

        // تعيين بيانات الجلسة
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['session_id'] = $sessionId;
        $_SESSION['username'] = $user['username'];
        $_SESSION['full_name'] = $user['full_name'];
        $_SESSION['user_type'] = $user['user_type'];
        $_SESSION['company_name'] = $user['company_name'];

        // تعيين كوكيز التذكر
        if ($remember && $rememberToken) {
            setcookie('remember_token', $rememberToken, time() + (30 * 24 * 3600), '/'); // 30 يوم
        }
    }

    /**
     * التحقق من صحة الجلسة
     */
    private function validateSession($sessionId) {
        $query = "SELECT * FROM sessions WHERE id = ? AND expires_at > NOW()";
        $stmt = $this->db->prepare($query);
        $stmt->execute([$sessionId]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC) !== false;
    }

    /**
     * تحديث نشاط الجلسة
     */
    private function updateSessionActivity($sessionId) {
        $query = "UPDATE sessions SET last_activity = NOW() WHERE id = ?";
        $stmt = $this->db->prepare($query);
        $stmt->execute([$sessionId]);
    }

    /**
     * حذف الجلسة
     */
    private function deleteSession($sessionId) {
        $query = "DELETE FROM sessions WHERE id = ?";
        $stmt = $this->db->prepare($query);
        $stmt->execute([$sessionId]);
    }

    /**
     * التحقق من رمز التذكر
     */
    private function checkRememberToken() {
        if (!isset($_COOKIE['remember_token'])) {
            return false;
        }

        $query = "SELECT u.* FROM users u 
                  JOIN sessions s ON u.id = s.user_id 
                  WHERE s.remember_token = ? AND s.expires_at > NOW()";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute([$_COOKIE['remember_token']]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($user) {
            $this->createSession($user, true);
            return true;
        }

        return false;
    }

    /**
     * الحصول على المستخدم بواسطة اسم المستخدم أو البريد الإلكتروني
     */
    private function getUserByUsernameOrEmail($username) {
        $query = "SELECT * FROM users WHERE (username = ? OR email = ?) AND status = 'active'";
        $stmt = $this->db->prepare($query);
        $stmt->execute([$username, $username]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على المستخدم بواسطة المعرف
     */
    private function getUserById($userId) {
        $query = "SELECT * FROM users WHERE id = ?";
        $stmt = $this->db->prepare($query);
        $stmt->execute([$userId]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * تسجيل محاولة دخول فاشلة
     */
    private function recordFailedLogin($username) {
        $query = "UPDATE users SET login_attempts = login_attempts + 1 
                  WHERE username = ? OR email = ?";
        $stmt = $this->db->prepare($query);
        $stmt->execute([$username, $username]);

        // قفل الحساب إذا تجاوز الحد الأقصى
        $user = $this->getUserByUsernameOrEmail($username);
        if ($user && $user['login_attempts'] >= $this->maxLoginAttempts) {
            $lockUntil = date('Y-m-d H:i:s', time() + $this->lockoutTime);
            $query = "UPDATE users SET locked_until = ? WHERE id = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$lockUntil, $user['id']]);
        }
    }

    /**
     * التحقق من قفل الحساب
     */
    private function isAccountLocked($username) {
        $user = $this->getUserByUsernameOrEmail($username);
        if (!$user) return false;

        if ($user['locked_until'] && strtotime($user['locked_until']) > time()) {
            return true;
        }

        return false;
    }

    /**
     * إعادة تعيين محاولات تسجيل الدخول
     */
    private function resetLoginAttempts($userId) {
        $query = "UPDATE users SET login_attempts = 0, locked_until = NULL WHERE id = ?";
        $stmt = $this->db->prepare($query);
        $stmt->execute([$userId]);
    }

    /**
     * تحديث آخر تسجيل دخول
     */
    private function updateLastLogin($userId) {
        $query = "UPDATE users SET last_login = NOW() WHERE id = ?";
        $stmt = $this->db->prepare($query);
        $stmt->execute([$userId]);
    }

    /**
     * توليد معرف جلسة
     */
    private function generateSessionId() {
        return bin2hex(random_bytes(32));
    }

    /**
     * توليد رمز التذكر
     */
    private function generateRememberToken() {
        return bin2hex(random_bytes(32));
    }

    /**
     * تنظيف بيانات المستخدم للعرض
     */
    private function sanitizeUserData($user) {
        unset($user['password_hash']);
        unset($user['login_attempts']);
        unset($user['locked_until']);
        return $user;
    }

    /**
     * تنظيف الجلسات المنتهية الصلاحية
     */
    public function cleanExpiredSessions() {
        $query = "DELETE FROM sessions WHERE expires_at < NOW()";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
    }
}

// إنشاء مثيل من نظام المصادقة
$auth = new Auth();

// تنظيف الجلسات المنتهية الصلاحية (بشكل عشوائي)
if (rand(1, 100) <= 5) { // 5% احتمال
    $auth->cleanExpiredSessions();
}
?>
