<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../includes/middleware.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً']);
    exit();
}

$currentUser = getCurrentUser();

try {
    // البحث عن القالب الافتراضي للمستخدم
    $query = "SELECT t.* FROM invoice_templates t 
              JOIN user_template_preferences utp ON t.id = utp.template_id 
              WHERE utp.user_id = ? AND utp.is_default = 1 AND t.is_active = 1";
    
    $stmt = $db->prepare($query);
    $stmt->execute([$currentUser['id']]);
    $template = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($template) {
        echo json_encode([
            'success' => true,
            'template' => $template
        ]);
    } else {
        // إرجاع القالب الافتراضي الأول إذا لم يكن للمستخدم قالب محدد
        $query = "SELECT * FROM invoice_templates WHERE is_active = 1 ORDER BY sort_order ASC LIMIT 1";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $defaultTemplate = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($defaultTemplate) {
            echo json_encode([
                'success' => true,
                'template' => $defaultTemplate
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'لا توجد قوالب متاحة'
            ]);
        }
    }
    
} catch (Exception $e) {
    Config::logError("Error getting user template: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ أثناء تحميل القالب'
    ]);
}
?>
