<?php
require_once 'includes/middleware.php';

// إعادة توجيه المستخدمين المسجلين إلى لوحة التحكم
requireGuest();

$error = '';
$success = '';
$formData = [];

// معالجة التسجيل
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $formData = [
        'full_name' => trim($_POST['full_name'] ?? ''),
        'company_name' => trim($_POST['company_name'] ?? ''),
        'username' => trim($_POST['username'] ?? ''),
        'email' => trim($_POST['email'] ?? ''),
        'phone' => trim($_POST['phone'] ?? ''),
        'password' => $_POST['password'] ?? '',
        'password_confirm' => $_POST['password_confirm'] ?? ''
    ];
    
    $csrf_token = $_POST['csrf_token'] ?? '';

    // التحقق من CSRF Token
    if (!$middleware->validateCSRF($csrf_token)) {
        $error = 'رمز الأمان غير صحيح';
    } else {
        // التحقق من البيانات
        $validation = validateRegistrationData($formData);
        
        if ($validation['valid']) {
            // إنشاء المستخدم
            $result = createUser($formData);
            
            if ($result['success']) {
                $success = $result['message'];
                $formData = []; // مسح البيانات بعد النجاح
            } else {
                $error = $result['message'];
            }
        } else {
            $error = $validation['message'];
        }
    }
}

function validateRegistrationData($data) {
    global $middleware;
    
    // التحقق من الحقول المطلوبة
    $requiredFields = ['full_name', 'username', 'email', 'password', 'password_confirm'];
    foreach ($requiredFields as $field) {
        if (empty($data[$field])) {
            return ['valid' => false, 'message' => 'جميع الحقول المطلوبة يجب ملؤها'];
        }
    }
    
    // التحقق من صحة البريد الإلكتروني
    if (!$middleware->validateEmail($data['email'])) {
        return ['valid' => false, 'message' => 'البريد الإلكتروني غير صحيح'];
    }
    
    // التحقق من تطابق كلمة المرور
    if ($data['password'] !== $data['password_confirm']) {
        return ['valid' => false, 'message' => 'كلمة المرور وتأكيد كلمة المرور غير متطابقتين'];
    }
    
    // التحقق من قوة كلمة المرور
    $passwordValidation = $middleware->validatePassword($data['password']);
    if (!$passwordValidation['valid']) {
        return ['valid' => false, 'message' => $passwordValidation['message']];
    }
    
    // التحقق من طول اسم المستخدم
    if (strlen($data['username']) < 3) {
        return ['valid' => false, 'message' => 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل'];
    }
    
    // التحقق من عدم وجود أحرف خاصة في اسم المستخدم
    if (!preg_match('/^[a-zA-Z0-9_]+$/', $data['username'])) {
        return ['valid' => false, 'message' => 'اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط'];
    }
    
    return ['valid' => true, 'message' => ''];
}

function createUser($data) {
    global $db;
    
    try {
        // التحقق من عدم وجود اسم المستخدم أو البريد الإلكتروني
        $checkQuery = "SELECT id FROM users WHERE username = ? OR email = ?";
        $checkStmt = $db->prepare($checkQuery);
        $checkStmt->execute([$data['username'], $data['email']]);
        
        if ($checkStmt->fetch()) {
            return ['success' => false, 'message' => 'اسم المستخدم أو البريد الإلكتروني موجود بالفعل'];
        }
        
        // تشفير كلمة المرور
        $passwordHash = password_hash($data['password'], PASSWORD_DEFAULT);
        
        // إدراج المستخدم الجديد
        $insertQuery = "INSERT INTO users (username, email, password_hash, full_name, company_name, phone, user_type, status, email_verified) 
                        VALUES (?, ?, ?, ?, ?, ?, 'user', 'active', FALSE)";
        
        $insertStmt = $db->prepare($insertQuery);
        $insertStmt->execute([
            $data['username'],
            $data['email'],
            $passwordHash,
            $data['full_name'],
            $data['company_name'],
            $data['phone']
        ]);
        
        return ['success' => true, 'message' => 'تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول'];
        
    } catch (Exception $e) {
        Config::logError("Registration error: " . $e->getMessage());
        return ['success' => false, 'message' => 'حدث خطأ أثناء إنشاء الحساب'];
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد - نظام إنشاء الفواتير</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }

        .register-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 500px;
        }

        .register-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .register-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }

        .register-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 14px;
        }

        .register-form {
            padding: 30px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #2c3e50;
            font-size: 14px;
        }

        .required {
            color: #e74c3c;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background-color: #f8f9fa;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            background-color: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-control.error {
            border-color: #e74c3c;
            background-color: #fdf2f2;
        }

        .btn-register {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-register:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .alert {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .alert-error {
            background-color: #fdf2f2;
            color: #e74c3c;
            border: 1px solid #f5c6cb;
        }

        .alert-success {
            background-color: #f2f8f2;
            color: #28a745;
            border: 1px solid #c3e6cb;
        }

        .register-footer {
            text-align: center;
            padding: 20px 30px;
            background-color: #f8f9fa;
            border-top: 1px solid #e1e8ed;
        }

        .register-footer a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .register-footer a:hover {
            color: #764ba2;
        }

        .password-strength {
            margin-top: 5px;
            font-size: 12px;
        }

        .strength-weak { color: #e74c3c; }
        .strength-medium { color: #f39c12; }
        .strength-strong { color: #27ae60; }

        @media (max-width: 600px) {
            .form-row {
                grid-template-columns: 1fr;
                gap: 0;
            }
            
            .register-container {
                margin: 10px;
            }
            
            .register-form {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-header">
            <h1><i class="fas fa-user-plus"></i> إنشاء حساب جديد</h1>
            <p>انضم إلى نظام إدارة الفواتير</p>
        </div>

        <div class="register-form">
            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success); ?>
                    <br><br>
                    <a href="login.php" class="btn-register" style="display: inline-block; text-decoration: none; margin: 0;">
                        تسجيل الدخول الآن
                    </a>
                </div>
            <?php else: ?>
                <form method="POST" action="" id="registerForm">
                    <?php echo csrf_field(); ?>
                    
                    <div class="form-group">
                        <label for="full_name">الاسم الكامل <span class="required">*</span></label>
                        <input type="text" 
                               id="full_name" 
                               name="full_name" 
                               class="form-control" 
                               value="<?php echo htmlspecialchars($formData['full_name'] ?? ''); ?>"
                               required>
                    </div>

                    <div class="form-group">
                        <label for="company_name">اسم الشركة</label>
                        <input type="text" 
                               id="company_name" 
                               name="company_name" 
                               class="form-control" 
                               value="<?php echo htmlspecialchars($formData['company_name'] ?? ''); ?>">
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="username">اسم المستخدم <span class="required">*</span></label>
                            <input type="text" 
                                   id="username" 
                                   name="username" 
                                   class="form-control" 
                                   value="<?php echo htmlspecialchars($formData['username'] ?? ''); ?>"
                                   required>
                        </div>

                        <div class="form-group">
                            <label for="phone">رقم الهاتف</label>
                            <input type="tel" 
                                   id="phone" 
                                   name="phone" 
                                   class="form-control" 
                                   value="<?php echo htmlspecialchars($formData['phone'] ?? ''); ?>">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="email">البريد الإلكتروني <span class="required">*</span></label>
                        <input type="email" 
                               id="email" 
                               name="email" 
                               class="form-control" 
                               value="<?php echo htmlspecialchars($formData['email'] ?? ''); ?>"
                               required>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="password">كلمة المرور <span class="required">*</span></label>
                            <input type="password" 
                                   id="password" 
                                   name="password" 
                                   class="form-control" 
                                   required>
                            <div id="passwordStrength" class="password-strength"></div>
                        </div>

                        <div class="form-group">
                            <label for="password_confirm">تأكيد كلمة المرور <span class="required">*</span></label>
                            <input type="password" 
                                   id="password_confirm" 
                                   name="password_confirm" 
                                   class="form-control" 
                                   required>
                        </div>
                    </div>

                    <button type="submit" class="btn-register" id="submitBtn">
                        <i class="fas fa-user-plus"></i> إنشاء الحساب
                    </button>
                </form>
            <?php endif; ?>
        </div>

        <div class="register-footer">
            <p>لديك حساب بالفعل؟ <a href="login.php">تسجيل الدخول</a></p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('registerForm');
            const submitBtn = document.getElementById('submitBtn');
            const passwordInput = document.getElementById('password');
            const passwordConfirmInput = document.getElementById('password_confirm');
            const passwordStrength = document.getElementById('passwordStrength');

            // التحقق من قوة كلمة المرور
            passwordInput.addEventListener('input', function() {
                const password = this.value;
                const strength = checkPasswordStrength(password);
                
                passwordStrength.textContent = strength.text;
                passwordStrength.className = 'password-strength ' + strength.class;
            });

            // التحقق من تطابق كلمة المرور
            passwordConfirmInput.addEventListener('input', function() {
                if (this.value !== passwordInput.value) {
                    this.classList.add('error');
                } else {
                    this.classList.remove('error');
                }
            });

            // معالجة إرسال النموذج
            form.addEventListener('submit', function() {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري إنشاء الحساب...';
                submitBtn.disabled = true;
            });

            function checkPasswordStrength(password) {
                if (password.length < 6) {
                    return { text: 'كلمة مرور ضعيفة', class: 'strength-weak' };
                }
                
                let score = 0;
                if (password.length >= 8) score++;
                if (/[a-z]/.test(password)) score++;
                if (/[A-Z]/.test(password)) score++;
                if (/[0-9]/.test(password)) score++;
                if (/[^A-Za-z0-9]/.test(password)) score++;
                
                if (score < 3) {
                    return { text: 'كلمة مرور ضعيفة', class: 'strength-weak' };
                } else if (score < 4) {
                    return { text: 'كلمة مرور متوسطة', class: 'strength-medium' };
                } else {
                    return { text: 'كلمة مرور قوية', class: 'strength-strong' };
                }
            }
        });
    </script>
</body>
</html>
