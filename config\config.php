<?php
// إعدادات النظام العامة
define('SYSTEM_NAME', 'نظام إنشاء الفواتير');
define('SYSTEM_VERSION', '1.0.0');
define('SYSTEM_AUTHOR', 'Invoice System');

// إعدادات الشركة الافتراضية
define('DEFAULT_COMPANY_NAME', 'شركة الفواتير المحدودة');
define('DEFAULT_COMPANY_ID', '0504280365');
define('DEFAULT_COMPANY_COUNTRY', 'المملكة العربية السعودية');
define('DEFAULT_COMPANY_PHONE', '+966504280365');
define('DEFAULT_COMPANY_EMAIL', '<EMAIL>');

// إعدادات الفواتير
define('INVOICE_PREFIX', '');
define('INVOICE_NUMBER_LENGTH', 6);
define('DEFAULT_TAX_RATE', 15);
define('DEFAULT_CURRENCY', 'ر.س');
define('DEFAULT_CURRENCY_SYMBOL', 'ر.س');

// إعدادات التاريخ والوقت
define('DEFAULT_TIMEZONE', 'Asia/Riyadh');
define('DATE_FORMAT', 'Y-m-d');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');
define('DISPLAY_DATE_FORMAT', 'd/m/Y');

// إعدادات رفع الملفات
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('MAX_IMAGE_SIZE', 2 * 1024 * 1024); // 2MB
define('UPLOAD_PATH', 'uploads/');
define('LOGO_PATH', 'uploads/logos/');
define('ATTACHMENT_PATH', 'uploads/attachments/');

// أنواع الملفات المسموحة
define('ALLOWED_IMAGE_TYPES', ['image/jpeg', 'image/png', 'image/gif', 'image/webp']);
define('ALLOWED_DOCUMENT_TYPES', [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain'
]);

// إعدادات الأمان
define('ENABLE_CSRF_PROTECTION', true);
define('SESSION_TIMEOUT', 3600); // ساعة واحدة
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 دقيقة

// إعدادات البريد الإلكتروني (للمستقبل)
define('SMTP_HOST', 'localhost');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('SMTP_ENCRYPTION', 'tls');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'نظام الفواتير');

// إعدادات قاعدة البيانات الإضافية
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', 'utf8mb4_unicode_ci');

// إعدادات التطبيق
define('DEBUG_MODE', false);
define('LOG_ERRORS', true);
define('LOG_PATH', 'logs/');

// إعدادات الصفحات
define('ITEMS_PER_PAGE', 10);
define('MAX_ITEMS_PER_PAGE', 100);

// حالات الفاتورة
define('INVOICE_STATUS_DRAFT', 'draft');
define('INVOICE_STATUS_SENT', 'sent');
define('INVOICE_STATUS_PAID', 'paid');
define('INVOICE_STATUS_OVERDUE', 'overdue');

// ألوان حالات الفاتورة
define('STATUS_COLORS', [
    'draft' => '#ffc107',
    'sent' => '#17a2b8',
    'paid' => '#28a745',
    'overdue' => '#dc3545'
]);

// نصوص حالات الفاتورة بالعربية
define('STATUS_TEXTS', [
    'draft' => 'مسودة',
    'sent' => 'مرسلة',
    'paid' => 'مدفوعة',
    'overdue' => 'متأخرة'
]);

// إعدادات التصدير
define('ENABLE_PDF_EXPORT', true);
define('ENABLE_EXCEL_EXPORT', true);
define('PDF_ORIENTATION', 'portrait');
define('PDF_UNIT', 'mm');
define('PDF_FORMAT', 'A4');

// إعدادات النسخ الاحتياطي
define('ENABLE_AUTO_BACKUP', false);
define('BACKUP_INTERVAL', 86400); // يومياً
define('BACKUP_PATH', 'backups/');
define('MAX_BACKUP_FILES', 30);

// إعدادات الإشعارات
define('ENABLE_EMAIL_NOTIFICATIONS', false);
define('ENABLE_SMS_NOTIFICATIONS', false);
define('NOTIFICATION_DAYS_BEFORE_DUE', 3);

// إعدادات API
define('API_VERSION', 'v1');
define('API_RATE_LIMIT', 100); // طلب في الدقيقة
define('API_ENABLE_CORS', true);

// دوال مساعدة
class Config {
    
    /**
     * الحصول على إعدادات الشركة من قاعدة البيانات
     */
    public static function getCompanySettings() {
        try {
            require_once 'database.php';
            global $db;
            
            $query = "SELECT * FROM company_settings LIMIT 1";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $settings = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($settings) {
                return $settings;
            }
        } catch (Exception $e) {
            error_log("Error loading company settings: " . $e->getMessage());
        }
        
        // إرجاع الإعدادات الافتراضية
        return [
            'company_name' => DEFAULT_COMPANY_NAME,
            'company_id' => DEFAULT_COMPANY_ID,
            'country' => DEFAULT_COMPANY_COUNTRY,
            'phone' => DEFAULT_COMPANY_PHONE,
            'email' => DEFAULT_COMPANY_EMAIL,
            'address' => '',
            'logo_path' => ''
        ];
    }
    
    /**
     * تنسيق التاريخ للعرض
     */
    public static function formatDate($date, $format = DISPLAY_DATE_FORMAT) {
        if (empty($date)) return '';
        
        try {
            $dateObj = new DateTime($date);
            return $dateObj->format($format);
        } catch (Exception $e) {
            return $date;
        }
    }
    
    /**
     * تنسيق المبلغ للعرض
     */
    public static function formatAmount($amount, $currency = DEFAULT_CURRENCY) {
        return number_format($amount, 2) . ' ' . $currency;
    }
    
    /**
     * الحصول على نص حالة الفاتورة
     */
    public static function getStatusText($status) {
        $statusTexts = STATUS_TEXTS;
        return $statusTexts[$status] ?? $status;
    }
    
    /**
     * الحصول على لون حالة الفاتورة
     */
    public static function getStatusColor($status) {
        $statusColors = STATUS_COLORS;
        return $statusColors[$status] ?? '#6c757d';
    }
    
    /**
     * إنشاء رقم فاتورة جديد
     */
    public static function generateInvoiceNumber() {
        try {
            require_once 'database.php';
            global $db;
            
            $query = "SELECT MAX(CAST(SUBSTRING(invoice_number, 1) AS UNSIGNED)) as max_num FROM invoices";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $nextNumber = ($result['max_num'] ?? 0) + 1;
            return INVOICE_PREFIX . str_pad($nextNumber, INVOICE_NUMBER_LENGTH, '0', STR_PAD_LEFT);
        } catch (Exception $e) {
            error_log("Error generating invoice number: " . $e->getMessage());
            return INVOICE_PREFIX . str_pad(1, INVOICE_NUMBER_LENGTH, '0', STR_PAD_LEFT);
        }
    }
    
    /**
     * تسجيل الأخطاء
     */
    public static function logError($message, $file = '', $line = '') {
        if (LOG_ERRORS) {
            $logMessage = date('[Y-m-d H:i:s] ') . $message;
            if ($file) $logMessage .= " in $file";
            if ($line) $logMessage .= " on line $line";
            $logMessage .= PHP_EOL;
            
            $logDir = LOG_PATH;
            if (!file_exists($logDir)) {
                mkdir($logDir, 0755, true);
            }
            
            error_log($logMessage, 3, $logDir . 'error_' . date('Y-m-d') . '.log');
        }
    }
}

// تعيين المنطقة الزمنية
date_default_timezone_set(DEFAULT_TIMEZONE);

// تعيين ترميز الأحرف
if (function_exists('mb_internal_encoding')) {
    mb_internal_encoding('UTF-8');
}

// إعدادات PHP
ini_set('display_errors', DEBUG_MODE ? 1 : 0);
ini_set('log_errors', LOG_ERRORS ? 1 : 0);

if (LOG_ERRORS && !file_exists(LOG_PATH)) {
    mkdir(LOG_PATH, 0755, true);
}
?>
