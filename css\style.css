* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background-color: #f8f9fa;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.invoice-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.invoice-header h1 {
    font-size: 28px;
    font-weight: 600;
    color: #2c3e50;
}

.header-buttons {
    display: flex;
    gap: 10px;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #218838;
}

/* Main Content */
.invoice-content {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 30px;
}

.main-section {
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Logo Section */
.logo-section {
    margin-bottom: 30px;
}

.logo-upload {
    width: 200px;
    height: 150px;
    border: 2px dashed #ddd;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
}

.logo-upload:hover {
    border-color: #007bff;
    background-color: #e3f2fd;
}

.logo-upload i {
    font-size: 24px;
    color: #6c757d;
    margin-bottom: 10px;
}

.logo-upload p {
    text-align: center;
    color: #6c757d;
    font-size: 14px;
}

.select-file {
    color: #007bff;
    text-decoration: underline;
    cursor: pointer;
}

/* Company Info */
.company-info {
    position: absolute;
    top: 30px;
    left: 250px;
}

.company-details {
    text-align: right;
}

.invoice-number {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
}

.company-id, .country {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 3px;
}

.edit-business-info {
    color: #007bff;
    text-decoration: none;
    font-size: 14px;
}

.edit-business-info:hover {
    text-decoration: underline;
}

/* Invoice Details */
.invoice-details {
    margin: 40px 0;
}

.detail-row {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 20px;
}

.detail-group {
    display: flex;
    flex-direction: column;
}

.detail-group label {
    font-weight: 600;
    margin-bottom: 8px;
    color: #2c3e50;
    font-size: 14px;
}

.form-control {
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.amount-display {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #28a745;
}

.create-client {
    color: #007bff;
    text-decoration: none;
    font-size: 14px;
    margin-top: 5px;
}

.create-client:hover {
    text-decoration: underline;
}

/* Invoice Table */
.invoice-table {
    margin: 30px 0;
}

#itemsTable {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

#itemsTable th {
    background-color: #f8f9fa;
    padding: 15px 10px;
    text-align: right;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
    color: #2c3e50;
}

#itemsTable td {
    padding: 15px 10px;
    border-bottom: 1px solid #dee2e6;
    vertical-align: top;
}

.item-row .form-control {
    border: none;
    padding: 5px;
    margin-bottom: 5px;
}

.item-name {
    font-weight: 600;
}

.item-description {
    font-size: 12px;
    color: #6c757d;
}

.tax-note {
    color: #007bff;
    font-size: 12px;
}

.line-total {
    font-weight: 600;
    color: #28a745;
}

.btn-remove {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    font-size: 16px;
    padding: 5px;
}

.btn-remove:hover {
    color: #c82333;
}

.add-line-btn {
    background: none;
    border: 2px dashed #007bff;
    color: #007bff;
    padding: 15px;
    width: 100%;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.add-line-btn:hover {
    background-color: #e3f2fd;
}

/* Invoice Summary */
.invoice-summary {
    margin-left: auto;
    width: 300px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 20px;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 14px;
}

.summary-row:last-child {
    margin-bottom: 0;
}

.discount-input, .tax-select {
    width: 80px;
    padding: 5px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
}

.total-row {
    border-top: 2px solid #dee2e6;
    padding-top: 15px;
    font-weight: 600;
    font-size: 16px;
}

.amount-due-row {
    background-color: #f8f9fa;
    padding: 15px;
    margin: 0 -20px -20px -20px;
    border-radius: 0 0 6px 6px;
    font-weight: 600;
    color: #28a745;
}

.request-deposit {
    color: #007bff;
    text-decoration: none;
    font-size: 12px;
    margin-right: 10px;
}

/* Notes and Terms */
.notes-section, .terms-section {
    margin: 30px 0;
}

.notes-section label, .terms-section label {
    display: block;
    font-weight: 600;
    margin-bottom: 8px;
    color: #2c3e50;
}

.notes-section textarea, .terms-section textarea {
    width: 100%;
    min-height: 80px;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    resize: vertical;
    font-family: inherit;
}

/* Settings Sidebar */
.settings-sidebar {
    background: white;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    height: fit-content;
}

.settings-sidebar h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 5px;
    color: #2c3e50;
}

.sidebar-subtitle {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 25px;
}

.setting-item {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.setting-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.setting-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    cursor: pointer;
}

.setting-header i:first-child {
    margin-left: 10px;
    color: #6c757d;
}

.setting-header span:first-of-type {
    flex: 1;
    font-weight: 500;
}

.setting-status {
    color: #6c757d;
    font-size: 14px;
    margin-left: 10px;
}

.setting-header i:last-child {
    color: #6c757d;
    font-size: 12px;
}

.setting-description {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 10px;
}

.payment-methods {
    display: flex;
    align-items: center;
    gap: 5px;
}

.payment-methods img {
    border-radius: 3px;
}

.payment-methods span {
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    color: #6c757d;
}

/* Attachments */
.attachments-section {
    background: white;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-top: 20px;
}

.attachments-section h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #2c3e50;
}

.attachment-upload {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
}

.attachment-upload:hover {
    border-color: #007bff;
    background-color: #e3f2fd;
}

.attachment-upload i {
    font-size: 24px;
    color: #6c757d;
    margin-bottom: 10px;
}

.attachment-upload span {
    color: #6c757d;
    font-size: 16px;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 500px;
    max-width: 90%;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.close {
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    color: #6c757d;
}

.close:hover {
    color: #000;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #2c3e50;
}

/* Responsive */
@media (max-width: 1200px) {
    .invoice-content {
        grid-template-columns: 1fr;
    }
    
    .settings-sidebar {
        order: -1;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .invoice-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .detail-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .company-info {
        position: static;
        text-align: center;
        margin: 20px 0;
    }
    
    .invoice-summary {
        width: 100%;
        margin: 20px 0;
    }
}
