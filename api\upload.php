<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// إنشاء مجلد الرفع إذا لم يكن موجوداً
$uploadDir = '../uploads/';
if (!file_exists($uploadDir)) {
    mkdir($uploadDir, 0755, true);
}

// إنشاء مجلدات فرعية
$logoDir = $uploadDir . 'logos/';
$attachmentDir = $uploadDir . 'attachments/';

if (!file_exists($logoDir)) {
    mkdir($logoDir, 0755, true);
}

if (!file_exists($attachmentDir)) {
    mkdir($attachmentDir, 0755, true);
}

class FileUploader {
    private $allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    private $allowedDocumentTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/plain',
        'image/jpeg',
        'image/png',
        'image/gif'
    ];
    
    private $maxFileSize = 5 * 1024 * 1024; // 5MB
    private $maxImageSize = 2 * 1024 * 1024; // 2MB

    public function uploadLogo($file) {
        // التحقق من وجود الملف
        if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
            return ['success' => false, 'message' => 'لم يتم رفع الملف بشكل صحيح'];
        }

        // التحقق من نوع الملف
        if (!in_array($file['type'], $this->allowedImageTypes)) {
            return ['success' => false, 'message' => 'نوع الملف غير مدعوم. يُسمح فقط بملفات الصور (JPEG, PNG, GIF, WebP)'];
        }

        // التحقق من حجم الملف
        if ($file['size'] > $this->maxImageSize) {
            return ['success' => false, 'message' => 'حجم الملف كبير جداً. الحد الأقصى 2 ميجابايت'];
        }

        // التحقق من أن الملف صورة حقيقية
        $imageInfo = getimagesize($file['tmp_name']);
        if ($imageInfo === false) {
            return ['success' => false, 'message' => 'الملف ليس صورة صحيحة'];
        }

        // إنشاء اسم ملف فريد
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $fileName = 'logo_' . time() . '_' . uniqid() . '.' . $extension;
        $filePath = '../uploads/logos/' . $fileName;

        // رفع الملف
        if (move_uploaded_file($file['tmp_name'], $filePath)) {
            return [
                'success' => true,
                'message' => 'تم رفع الشعار بنجاح',
                'data' => [
                    'file_name' => $fileName,
                    'file_path' => 'uploads/logos/' . $fileName,
                    'file_url' => 'uploads/logos/' . $fileName
                ]
            ];
        } else {
            return ['success' => false, 'message' => 'فشل في رفع الملف'];
        }
    }

    public function uploadAttachment($file, $invoiceId = null) {
        // التحقق من وجود الملف
        if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
            return ['success' => false, 'message' => 'لم يتم رفع الملف بشكل صحيح'];
        }

        // التحقق من نوع الملف
        if (!in_array($file['type'], $this->allowedDocumentTypes)) {
            return ['success' => false, 'message' => 'نوع الملف غير مدعوم'];
        }

        // التحقق من حجم الملف
        if ($file['size'] > $this->maxFileSize) {
            return ['success' => false, 'message' => 'حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت'];
        }

        // تنظيف اسم الملف
        $originalName = $file['name'];
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $baseName = pathinfo($originalName, PATHINFO_FILENAME);
        $baseName = preg_replace('/[^a-zA-Z0-9\-_\u0600-\u06FF]/', '_', $baseName);
        
        // إنشاء اسم ملف فريد
        $fileName = $baseName . '_' . time() . '_' . uniqid() . '.' . $extension;
        $filePath = '../uploads/attachments/' . $fileName;

        // رفع الملف
        if (move_uploaded_file($file['tmp_name'], $filePath)) {
            $result = [
                'success' => true,
                'message' => 'تم رفع المرفق بنجاح',
                'data' => [
                    'file_name' => $originalName,
                    'stored_name' => $fileName,
                    'file_path' => 'uploads/attachments/' . $fileName,
                    'file_size' => $file['size'],
                    'file_type' => $file['type']
                ]
            ];

            // حفظ معلومات المرفق في قاعدة البيانات إذا تم توفير معرف الفاتورة
            if ($invoiceId) {
                $this->saveAttachmentToDatabase($invoiceId, $originalName, 'uploads/attachments/' . $fileName, $file['size'], $file['type']);
            }

            return $result;
        } else {
            return ['success' => false, 'message' => 'فشل في رفع الملف'];
        }
    }

    private function saveAttachmentToDatabase($invoiceId, $fileName, $filePath, $fileSize, $fileType) {
        try {
            require_once '../config/database.php';
            
            $query = "INSERT INTO attachments (invoice_id, file_name, file_path, file_size, file_type) 
                      VALUES (?, ?, ?, ?, ?)";
            
            $stmt = $db->prepare($query);
            $stmt->execute([$invoiceId, $fileName, $filePath, $fileSize, $fileType]);
            
            return true;
        } catch (Exception $e) {
            error_log("Error saving attachment to database: " . $e->getMessage());
            return false;
        }
    }

    public function deleteFile($filePath) {
        $fullPath = '../' . $filePath;
        if (file_exists($fullPath)) {
            return unlink($fullPath);
        }
        return false;
    }

    public function getFileInfo($filePath) {
        $fullPath = '../' . $filePath;
        if (file_exists($fullPath)) {
            return [
                'exists' => true,
                'size' => filesize($fullPath),
                'modified' => filemtime($fullPath),
                'type' => mime_content_type($fullPath)
            ];
        }
        return ['exists' => false];
    }
}

// معالجة الطلبات
$method = $_SERVER['REQUEST_METHOD'];
$uploader = new FileUploader();

if ($method === 'POST') {
    $uploadType = $_POST['type'] ?? '';
    
    switch ($uploadType) {
        case 'logo':
            if (isset($_FILES['logo'])) {
                $result = $uploader->uploadLogo($_FILES['logo']);
                echo json_encode($result);
            } else {
                echo json_encode(['success' => false, 'message' => 'لم يتم العثور على ملف الشعار']);
            }
            break;
            
        case 'attachment':
            if (isset($_FILES['attachment'])) {
                $invoiceId = $_POST['invoice_id'] ?? null;
                $result = $uploader->uploadAttachment($_FILES['attachment'], $invoiceId);
                echo json_encode($result);
            } else {
                echo json_encode(['success' => false, 'message' => 'لم يتم العثور على ملف المرفق']);
            }
            break;
            
        case 'multiple_attachments':
            if (isset($_FILES['attachments'])) {
                $results = [];
                $invoiceId = $_POST['invoice_id'] ?? null;
                
                // معالجة ملفات متعددة
                $fileCount = count($_FILES['attachments']['name']);
                for ($i = 0; $i < $fileCount; $i++) {
                    $file = [
                        'name' => $_FILES['attachments']['name'][$i],
                        'type' => $_FILES['attachments']['type'][$i],
                        'tmp_name' => $_FILES['attachments']['tmp_name'][$i],
                        'error' => $_FILES['attachments']['error'][$i],
                        'size' => $_FILES['attachments']['size'][$i]
                    ];
                    
                    $result = $uploader->uploadAttachment($file, $invoiceId);
                    $results[] = $result;
                }
                
                echo json_encode(['success' => true, 'data' => $results]);
            } else {
                echo json_encode(['success' => false, 'message' => 'لم يتم العثور على ملفات المرفقات']);
            }
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'نوع الرفع غير محدد']);
            break;
    }
} elseif ($method === 'DELETE') {
    // حذف ملف
    $data = json_decode(file_get_contents("php://input"), true);
    
    if (isset($data['file_path'])) {
        $result = $uploader->deleteFile($data['file_path']);
        if ($result) {
            echo json_encode(['success' => true, 'message' => 'تم حذف الملف بنجاح']);
        } else {
            echo json_encode(['success' => false, 'message' => 'فشل في حذف الملف']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'مسار الملف غير محدد']);
    }
} elseif ($method === 'GET') {
    // الحصول على معلومات ملف
    if (isset($_GET['file_path'])) {
        $info = $uploader->getFileInfo($_GET['file_path']);
        echo json_encode(['success' => true, 'data' => $info]);
    } else {
        echo json_encode(['success' => false, 'message' => 'مسار الملف غير محدد']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مدعومة']);
}
?>
