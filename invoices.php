<?php
require_once 'includes/middleware.php';

// التحقق من تسجيل الدخول
requireAuth();

$user = getCurrentUser();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة الفواتير - نظام إنشاء الفواتير</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        .invoices-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .page-header h1 {
            font-size: 28px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .search-bar {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .search-input {
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
            width: 300px;
        }
        
        .invoices-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th {
            background-color: #f8f9fa;
            padding: 15px;
            text-align: right;
            font-weight: 600;
            border-bottom: 2px solid #dee2e6;
            color: #2c3e50;
        }
        
        .table td {
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
            vertical-align: middle;
        }
        
        .table tbody tr:hover {
            background-color: #f8f9fa;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .status-draft {
            background-color: #ffc107;
            color: #856404;
        }
        
        .status-sent {
            background-color: #17a2b8;
            color: white;
        }
        
        .status-paid {
            background-color: #28a745;
            color: white;
        }
        
        .status-overdue {
            background-color: #dc3545;
            color: white;
        }
        
        .amount {
            font-weight: 600;
            color: #28a745;
        }
        
        .actions {
            display: flex;
            gap: 5px;
        }
        
        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
            border-radius: 4px;
        }
        
        .btn-view {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
        }
        
        .btn-edit {
            background-color: #ffc107;
            color: #856404;
            border: none;
            cursor: pointer;
        }
        
        .btn-delete {
            background-color: #dc3545;
            color: white;
            border: none;
            cursor: pointer;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: 20px;
            color: #dee2e6;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            gap: 10px;
        }
        
        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .pagination button:hover {
            background-color: #f8f9fa;
        }
        
        .pagination button.active {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }

        .navbar {
            background: white;
            padding: 15px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .navbar-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .navbar-brand {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            text-decoration: none;
        }

        .navbar-nav {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .nav-link {
            color: #6c757d;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .nav-link:hover, .nav-link.active {
            background-color: #f8f9fa;
            color: #2c3e50;
        }

        .user-menu {
            position: relative;
            display: inline-block;
        }

        .user-toggle {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            background: #f8f9fa;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            color: #2c3e50;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 6px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            min-width: 180px;
            z-index: 1000;
        }

        .dropdown-item {
            display: block;
            padding: 10px 15px;
            color: #2c3e50;
            text-decoration: none;
            transition: background-color 0.3s ease;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar">
        <div class="navbar-content">
            <a href="dashboard.php" class="navbar-brand">
                <i class="fas fa-file-invoice"></i> نظام الفواتير
            </a>

            <div class="navbar-nav">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-home"></i> الرئيسية
                </a>
                <a href="index.php" class="nav-link">
                    <i class="fas fa-plus"></i> فاتورة جديدة
                </a>
                <a href="invoices.php" class="nav-link active">
                    <i class="fas fa-file-invoice-dollar"></i> الفواتير
                </a>

                <div class="user-menu">
                    <button class="user-toggle" onclick="toggleUserMenu()">
                        <i class="fas fa-user"></i>
                        <?php echo htmlspecialchars($user['full_name']); ?>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div id="userDropdown" class="dropdown-menu" style="display: none;">
                        <a href="profile.php" class="dropdown-item">
                            <i class="fas fa-user-edit"></i> الملف الشخصي
                        </a>
                        <a href="logout.php" class="dropdown-item">
                            <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="invoices-container">
        <div class="page-header">
            <h1>قائمة الفواتير</h1>
            <div class="search-bar">
                <input type="text" id="searchInput" class="search-input" placeholder="البحث في الفواتير...">
                <button class="btn btn-primary" onclick="searchInvoices()">
                    <i class="fas fa-search"></i>
                </button>
                <a href="index.php" class="btn btn-success">
                    <i class="fas fa-plus"></i> فاتورة جديدة
                </a>
            </div>
        </div>

        <div class="invoices-table">
            <table class="table" id="invoicesTable">
                <thead>
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>العميل</th>
                        <th>تاريخ الإصدار</th>
                        <th>تاريخ الاستحقاق</th>
                        <th>المبلغ</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="invoicesTableBody">
                    <!-- Invoices will be loaded here -->
                </tbody>
            </table>
            
            <div id="emptyState" class="empty-state" style="display: none;">
                <i class="fas fa-file-invoice"></i>
                <h3>لا توجد فواتير</h3>
                <p>ابدأ بإنشاء فاتورتك الأولى</p>
                <a href="index.php" class="btn btn-primary">إنشاء فاتورة جديدة</a>
            </div>
        </div>

        <div class="pagination" id="pagination">
            <!-- Pagination will be generated here -->
        </div>
    </div>

    <script>
        let invoices = [];
        let currentPage = 1;
        const itemsPerPage = 10;

        // Load invoices on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadInvoices();
        });

        // Load invoices from API
        function loadInvoices() {
            fetch('api/invoices.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        invoices = data.data;
                        displayInvoices();
                    } else {
                        console.error('Error loading invoices:', data.message);
                        showEmptyState();
                    }
                })
                .catch(error => {
                    console.error('Error loading invoices:', error);
                    showEmptyState();
                });
        }

        // Display invoices in table
        function displayInvoices(filteredInvoices = null) {
            const invoicesToShow = filteredInvoices || invoices;
            const tableBody = document.getElementById('invoicesTableBody');
            const emptyState = document.getElementById('emptyState');

            if (invoicesToShow.length === 0) {
                showEmptyState();
                return;
            }

            emptyState.style.display = 'none';
            
            // Calculate pagination
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const paginatedInvoices = invoicesToShow.slice(startIndex, endIndex);

            tableBody.innerHTML = '';
            
            paginatedInvoices.forEach(invoice => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${invoice.invoice_number}</td>
                    <td>${invoice.client_name || 'غير محدد'}</td>
                    <td>${formatDate(invoice.issue_date)}</td>
                    <td>${invoice.due_date ? formatDate(invoice.due_date) : '-'}</td>
                    <td class="amount">${parseFloat(invoice.total_amount || 0).toFixed(2)} ر.س</td>
                    <td><span class="status-badge status-${invoice.status}">${getStatusText(invoice.status)}</span></td>
                    <td class="actions">
                        <button class="btn btn-sm btn-view" onclick="viewInvoice(${invoice.id})" title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-edit" onclick="editInvoice(${invoice.id})" title="تحرير">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-delete" onclick="deleteInvoice(${invoice.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tableBody.appendChild(row);
            });

            generatePagination(invoicesToShow.length);
        }

        // Show empty state
        function showEmptyState() {
            document.getElementById('invoicesTableBody').innerHTML = '';
            document.getElementById('emptyState').style.display = 'block';
            document.getElementById('pagination').innerHTML = '';
        }

        // Format date
        function formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        }

        // Get status text in Arabic
        function getStatusText(status) {
            const statusMap = {
                'draft': 'مسودة',
                'sent': 'مرسلة',
                'paid': 'مدفوعة',
                'overdue': 'متأخرة'
            };
            return statusMap[status] || status;
        }

        // Search invoices
        function searchInvoices() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            if (!searchTerm) {
                displayInvoices();
                return;
            }

            const filteredInvoices = invoices.filter(invoice => 
                invoice.invoice_number.toLowerCase().includes(searchTerm) ||
                (invoice.client_name && invoice.client_name.toLowerCase().includes(searchTerm)) ||
                (invoice.reference_number && invoice.reference_number.toLowerCase().includes(searchTerm))
            );

            currentPage = 1;
            displayInvoices(filteredInvoices);
        }

        // Generate pagination
        function generatePagination(totalItems) {
            const totalPages = Math.ceil(totalItems / itemsPerPage);
            const pagination = document.getElementById('pagination');
            
            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }

            let paginationHTML = '';
            
            // Previous button
            if (currentPage > 1) {
                paginationHTML += `<button onclick="changePage(${currentPage - 1})">السابق</button>`;
            }

            // Page numbers
            for (let i = 1; i <= totalPages; i++) {
                const activeClass = i === currentPage ? 'active' : '';
                paginationHTML += `<button class="${activeClass}" onclick="changePage(${i})">${i}</button>`;
            }

            // Next button
            if (currentPage < totalPages) {
                paginationHTML += `<button onclick="changePage(${currentPage + 1})">التالي</button>`;
            }

            pagination.innerHTML = paginationHTML;
        }

        // Change page
        function changePage(page) {
            currentPage = page;
            displayInvoices();
        }

        // View invoice
        function viewInvoice(id) {
            window.open(`view_invoice.php?id=${id}`, '_blank');
        }

        // Edit invoice
        function editInvoice(id) {
            window.location.href = `index.php?edit=${id}`;
        }

        // Delete invoice
        function deleteInvoice(id) {
            if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
                fetch('api/invoices.php', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: id })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('تم حذف الفاتورة بنجاح');
                        loadInvoices();
                    } else {
                        alert('خطأ: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error deleting invoice:', error);
                    alert('حدث خطأ أثناء حذف الفاتورة');
                });
            }
        }

        // Search on Enter key
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchInvoices();
            }
        });

        function toggleUserMenu() {
            const dropdown = document.getElementById('userDropdown');
            dropdown.style.display = dropdown.style.display === 'none' ? 'block' : 'none';
        }

        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', function(event) {
            const userMenu = document.querySelector('.user-menu');
            const dropdown = document.getElementById('userDropdown');

            if (!userMenu.contains(event.target)) {
                dropdown.style.display = 'none';
            }
        });
    </script>
</body>
</html>
